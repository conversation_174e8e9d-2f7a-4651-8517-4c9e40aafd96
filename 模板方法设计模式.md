# 模板方法设计模式

### 一、设计思想
模板方法模式（Template Method Pattern）是一种**行为型设计模式**
核心思想是：  
**“定义一个算法的骨架（模板），将算法中可变的步骤延迟到子类中实现，从而在不改变算法整体结构的前提下，允许子类自定义特定步骤”**。

通俗来说，就是“先搭好框架，再填细节”：
- 父类（或接口）负责规定流程的“大步骤”（如“验证参数→处理数据→返回结果”），这些步骤的执行顺序是固定的。
- 子类（或实现类）负责实现流程中“可变的细节”（如不同场景下的数据处理逻辑）。

其设计初衷是**解决“流程固定但步骤实现多变”的问题**，通过抽取通用逻辑减少重复代码，同时保证流程的一致性。


### 二、核心结构
模板方法模式通常包含两个核心角色：
1. **抽象模板（Abstract Template）**：
    - 定义算法的骨架（模板方法），规定步骤的执行顺序。
    - 包含“抽象方法”（由子类实现的可变步骤）和“具体方法”（父类中固定实现的通用步骤）。

2. **具体实现（Concrete Implementation）**：
    - 继承或实现抽象模板，重写抽象方法，提供特定场景下的步骤实现。

**示例代码结构**：
```java
// 抽象模板（定义流程骨架）
abstract class AbstractExporter {
    // 模板方法：固定流程步骤
    public final String export(Long userId, String sessionId) {
        validateParams(userId, sessionId); // 通用步骤（具体方法）
        SessionInfo info = getSessionInfo(userId, sessionId); // 通用步骤（具体方法）
        return doExport(info); // 可变步骤（抽象方法，由子类实现）
    }

    // 具体方法：通用步骤（固定实现）
    private void validateParams(Long userId, String sessionId) {
        // 参数验证逻辑（所有导出格式通用）
    }

    private SessionInfo getSessionInfo(Long userId, String sessionId) {
        // 获取会话数据逻辑（所有导出格式通用）
    }

    // 抽象方法：可变步骤（由子类实现）
    protected abstract String doExport(SessionInfo info);
}

// 具体实现（HTML导出）
class HtmlExporter extends AbstractExporter {
    @Override
    protected String doExport(SessionInfo info) {
        // HTML格式生成逻辑（个性化实现）
    }
}

// 具体实现（XML导出）
class XmlExporter extends AbstractExporter {
    @Override
    protected String doExport(SessionInfo info) {
        // XML格式生成逻辑（个性化实现）
    }
}
```


### 三、适用业务场景
模板方法模式适用于**“流程固定但步骤实现多样化”**的场景，典型案例包括：

1. **框架级流程封装**：
    - 如Spring的`JdbcTemplate`（定义数据库操作流程：获取连接→执行SQL→处理结果→关闭连接，具体SQL由用户传入）。
    - 如Servlet的`doGet/doPost`（定义HTTP请求处理流程，具体业务逻辑由子类实现）。

2. **业务级导出/导入功能**：
    - 如上文的会话导出服务（通用流程：验证参数→获取会话→生成内容，不同格式（HTML/XML/Excel）的生成逻辑不同）。

3. **规则引擎/工作流**：
    - 如审批流程（固定步骤：提交→部门审批→总经理审批→归档，不同审批节点的校验逻辑由具体实现类定义）。

4. **跨平台适配**：
    - 如支付接口（固定流程：创建订单→调用支付→回调通知，不同支付渠道（微信/支付宝）的接口调用逻辑不同）。


### 四、优势与注意事项
- **优势**：
    1. 代码复用：通用逻辑只写一次，减少重复代码。
    2. 流程可控：父类固定流程顺序，避免子类破坏流程一致性。
    3. 扩展灵活：新增场景时只需实现子类，无需修改模板方法（符合“开闭原则”）。

- **注意事项**：
    1. 模板方法通常用`final`修饰，防止子类重写流程骨架。
    2. 抽象方法不宜过多，否则会导致子类实现复杂（“模板膨胀”问题）。


### 五、总结
模板方法模式的核心是**“分离变与不变”**：通过模板方法固定“不变的流程”，通过抽象方法开放“可变的细节”。在实际开发中，它是实现代码复用和扩展的重要手段，尤其适合框架设计和标准化业务流程场景。
