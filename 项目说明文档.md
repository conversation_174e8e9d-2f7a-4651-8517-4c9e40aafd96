# Ollama AI 聊天应用项目说明文档

**作者：CJX**  
**项目版本：0.0.1-SNAPSHOT**  
**技术栈：Spring Boot 3.3.3 + Spring AI 1.0.0 + Java 17**

## 📋 项目概述

这是一个基于Spring Boot和Spring AI框架开发的智能聊天应用，集成了Ollama本地大语言模型和OpenAI API，提供多模态AI分析、聊天对话、文件处理、图像生成等功能。项目采用现代化的分层架构设计，支持用户认证、会话管理、消息存储等完整功能。

## 🚀 核心功能

### 1. 智能聊天系统
- **普通对话**：支持与AI进行文本对话，自动保存聊天记录
- **流式对话**：实时流式输出AI回复，提升用户体验
- **会话管理**：自动创建和管理聊天会话，支持会话导出为HTML格式
- **多模型支持**：可配置使用不同的AI模型（gemma3、llama3.2、deepseek-r1等）

### 2. 多模态AI分析
- **图像识别**：上传图片进行AI分析和识别
- **文本分析**：上传文本文件进行AI内容分析
- **文件处理**：支持多种文件格式的上传和解析
- **历史记录**：保存所有多模态分析的历史记录

### 3. 图像生成功能
- **文生图**：基于文本描述生成图像（使用DALL-E 3模型）
- **质量控制**：支持不同质量级别的图像生成
- **历史管理**：保存用户的图像生成历史

### 4. 文件管理系统
- **文件上传**：支持图片、文档等多种文件类型上传
- **批量操作**：支持批量上传、删除图片
- **Excel导入**：支持通过Excel批量导入图片链接
- **文件验证**：完善的文件类型、大小验证机制

### 5. 用户认证系统
- **用户注册/登录**：完整的用户管理功能
- **JWT认证**：基于JWT的无状态身份验证
- **权限控制**：用户数据隔离，确保数据安全

### 6. 链接管理
- **关键词链接**：管理关键词与URL的映射关系
- **智能匹配**：支持关键词的智能匹配和替换
- **缓存优化**：使用缓存提升链接查询性能

## 🏗️ 技术架构

### 技术栈
- **后端框架**：Spring Boot 3.3.3
- **AI集成**：Spring AI 1.0.0
- **数据库**：MySQL 8.0
- **ORM框架**：MyBatis-Plus 3.5.5
- **身份认证**：JWT (jjwt 0.11.5)
- **文件处理**：Apache Tika 2.9.1, Apache POI 5.2.3
- **工具库**：Lombok, Commons-IO, CommonMark

### 项目结构
```
src/main/java/com/cjx/ollama/
├── config/          # 配置类
├── controller/      # 控制器层
├── service/         # 服务层
├── mapper/          # 数据访问层
├── pojo/           # 数据对象
│   ├── entity/     # 实体类
│   └── dto/        # 数据传输对象
├── utils/          # 工具类
├── exception/      # 异常处理
├── interceptor/    # 拦截器
└── result/         # 统一响应结果
```

### 数据库设计
- **user**：用户信息表
- **chat_session**：聊天会话表
- **chat_message**：聊天消息表
- **multimodel**：多模态分析记录表
- **text_image**：文生图记录表
- **image**：图片管理表
- **link**：链接管理表

## 🔧 配置说明

### 环境变量
项目需要配置以下环境变量：
- `OPENAI_API_KEY`：OpenAI API密钥
- `MYSQL_PASSWORD`：MySQL数据库密码

## 📡 API接口

### 聊天接口
- `GET /chat/getModel` - 获取当前AI模型
- `POST /chat/call` - 普通对话
- `POST /chat/stream/{streamId}` - 流式对话
- `DELETE /chat/stopStream/{streamId}` - 终止流式输出

### 多模态分析
- `POST /multimodal/imageAnalyze/{filename}` - 图像分析
- `POST /multimodal/textAnalyze/{filename}` - 文本分析
- `GET /multimodal/getMultimodelByUserId` - 获取分析历史

### 用户管理
- `POST /user/register` - 用户注册
- `POST /user/login` - 用户登录

### 文件管理
- `POST /upload/image` - 上传图片
- `POST /upload/text` - 上传文本文件
- `GET /image/getImageList` - 获取图片列表
- `POST /image/batchUploadImages` - 批量上传图片

### 图像生成
- `POST /textImage/generateImage` - 文生图
- `GET /textImage/getImageGenerateByUserId` - 获取生成历史

## 🚀 部署运行

### 环境要求
- Java 17+
- MySQL 8.0+
- Ollama服务（本地运行在11434端口）
- Maven 3.6+

### 运行步骤
1. 克隆项目到本地
2. 配置数据库连接和环境变量
3. 启动Ollama服务
4. 运行Maven命令：`mvn spring-boot:run`
5. 访问 http://localhost:8080

### Docker部署（可选）
项目支持Docker容器化部署，可以通过Maven插件构建OCI镜像。

## 🔍 项目特色

### 1. 现代化架构
- 采用Spring Boot 3.3.3最新版本
- 集成Spring AI官方AI框架
- 使用Java 17新特性

### 2. 完善的功能设计
- 支持多种AI模型切换
- 流式对话提升用户体验
- 多模态分析能力
- 完整的用户权限管理

### 3. 优秀的代码质量
- 分层架构清晰
- 统一异常处理
- 完善的参数验证
- 详细的日志记录

### 4. 安全性考虑
- JWT身份认证
- 数据权限隔离
- 文件上传安全验证
- 敏感信息环境变量管理

## 📈 扩展建议

### 性能优化
- 添加Redis缓存
- 数据库连接池优化
- 异步处理优化

### 功能扩展
- 支持更多AI模型
- 添加语音识别功能
- 实现实时协作功能
- 移动端适配

### 运维监控
- 添加健康检查端点
- 集成监控告警
- 性能指标收集

## 📞 联系方式

**作者**：CJX  
**项目地址**：[项目仓库地址]  
**技术支持**：[联系邮箱]

---

*本文档最后更新时间：2025年7月30日*
