package com.cjx.ollama.test;

import com.cjx.ollama.component.LinkLoader;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertSame;

@Slf4j
@SpringBootTest
class LinkLoaderBeanTest {

    @Autowired
    private LinkLoader linkLoader1;

    @Autowired
    private LinkLoader linkLoader2;

    @Test
    void testSingleton() {
        //断言
        assertSame(linkLoader1, linkLoader2, "两个 LinkLoader 应该是同一个单例实例");
        log.info("测试通过，两个实例是同一个单例");
    }
}
