<?xml version="1.0" encoding="UTF-8" ?>
<!-- WordPress eXtended RSS (WXR) 导出模板，用于WordPress内容导入 -->
<!-- 生成信息：保留此注释可帮助系统识别文件类型 -->
<!-- generator="WordPress/【你的WordPress版本，如6.8.2】" created="【生成日期，如2025-07-23 10:00】" -->

<rss version="2.0"
	xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
	xmlns:content="http://purl.org/rss/1.0/modules/content/"
	xmlns:wfw="http://wellformedweb.org/CommentAPI/"
	xmlns:dc="http://purl.org/dc/elements/1.1/"
	xmlns:wp="http://wordpress.org/export/1.2/"
>

<channel>
	<title>【你的网站标题，如“我的博客”】</title>
	<link>【你的网站URL，如https://example.com】</link>
	<description>【网站描述，可选】</description>
	<pubDate>【发布日期，如Wed, 23 Jul 2025 10:00:00 +0000】</pubDate>
	<language>【语言，如zh-Hans（简体中文）】</language>
	<wp:wxr_version>1.2</wp:wxr_version>
	<wp:base_site_url>【网站基础URL，与上方link一致】</wp:base_site_url>
	<wp:base_blog_url>【博客基础URL，与上方link一致】</wp:base_blog_url>

	<!-- 作者信息（必填，需与网站用户对应） -->
	<wp:author>
		<wp:author_id>【作者ID（纯数字），如1】</wp:author_id>
		<wp:author_login><![CDATA[【作者登录名，如admin】]]></wp:author_login>
		<wp:author_email><![CDATA[【作者邮箱，如*****************】]]></wp:author_email>
		<wp:author_display_name><![CDATA[【作者显示名，如“管理员”】]]></wp:author_display_name>
		<wp:author_first_name><![CDATA[【名，可选】]]></wp:author_first_name>
		<wp:author_last_name><![CDATA[【姓，可选】]]></wp:author_last_name>
	</wp:author>

	<!-- 分类信息（至少添加1个，如需多个可复制此块） -->
	<wp:category>
		<wp:term_id>【分类ID（纯数字），如1】</wp:term_id>
		<wp:category_nicename><![CDATA[【分类别名（英文/拼音），如tech】]]></wp:category_nicename>
		<wp:category_parent><![CDATA[【父分类别名，无则留空】]]></wp:category_parent>
		<wp:cat_name><![CDATA[【分类名称，如“科技”】]]></wp:cat_name>
	</wp:category>

	<!-- 网站标识（可选，建议保留） -->
	<generator>https://wordpress.org/?v=【你的WordPress版本，如6.8.2】</generator>
	<image>
		<url>【网站图标URL，如https://example.com/logo.png】</url>
		<title>【你的网站标题，与上方一致】</title>
		<link>【你的网站URL，与上方一致】</link>
		<width>32</width> <!-- 图标宽度固定32px -->
		<height>32</height> <!-- 图标高度固定32px -->
	</image>

	<!-- 文章内容块（单个文章模板，如需多文章可复制此<item>块） -->
	<item>
		<title><![CDATA[【文章标题，如“我的第一篇文章”】]]></title>
		<link>【文章链接，如https://example.com/?p=100】</link>
		<pubDate>【发布时间（GMT），如Wed, 23 Jul 2025 08:00:00 +0000】</pubDate>
		<dc:creator><![CDATA[【作者登录名，与上方wp:author_login一致】]]></dc:creator>
		<guid isPermaLink="false">【文章唯一标识链接，如https://example.com/?p=100】</guid>
		<description><![CDATA[【文章摘要，可选】]]></description>
		<!-- 文章正文（支持HTML标签，如<p><strong>等） -->
		<content:encoded><![CDATA[
【你的文章内容，例如：
<p>这是文章第一段内容。</p>
<h3>二级标题</h3>
<ul>
<li>列表项1</li>
<li>列表项2</li>
</ul>
]]></content:encoded>
		<excerpt:encoded><![CDATA[【文章摘要，可选，与description一致即可】]]></excerpt:encoded>
		<!-- 文章基础属性 -->
		<wp:post_id>【文章ID（纯数字，需唯一），如100】</wp:post_id>
		<wp:post_date><![CDATA[【发布时间（本地时间），如2025-07-23 16:00:00】]]></wp:post_date>
		<wp:post_date_gmt><![CDATA[【发布时间（GMT），如2025-07-23 08:00:00】]]></wp:post_date_gmt>
		<wp:post_modified><![CDATA[【最后修改时间（本地时间），如2025-07-23 17:00:00】]]></wp:post_modified>
		<wp:post_modified_gmt><![CDATA[【最后修改时间（GMT），如2025-07-23 09:00:00】]]></wp:post_modified_gmt>
		<wp:comment_status><![CDATA[【是否允许评论，open=允许，closed=关闭】]]></wp:comment_status>
		<wp:ping_status><![CDATA[【是否允许Ping，open=允许，closed=关闭】]]></wp:ping_status>
		<wp:post_name><![CDATA[【文章别名（URL友好格式），如my-first-post】]]></wp:post_name>
		<wp:status><![CDATA[【文章状态，publish=已发布，draft=草稿】]]></wp:status>
		<wp:post_parent>0</wp:post_parent> <!-- 父文章ID，无则固定为0 -->
		<wp:menu_order>0</wp:menu_order> <!-- 排序权重，默认0 -->
		<wp:post_type><![CDATA[post]]></wp:post_type> <!-- 类型，post=文章，page=页面 -->
		<wp:post_password><![CDATA[]]></wp:post_password> <!-- 文章密码，无则留空 -->
		<wp:is_sticky>0</wp:is_sticky> <!-- 是否置顶，1=置顶，0=不置顶 -->
		<!-- 文章所属分类（与上方wp:category的nicename对应） -->
		<category domain="category" nicename="【分类别名，如tech】"><![CDATA[【分类名称，如“科技”】]]></category>
		<!-- 文章扩展属性（无需修改，保留默认即可） -->
		<wp:postmeta>
			<wp:meta_key><![CDATA[_edit_last]]></wp:meta_key>
			<wp:meta_value><![CDATA[【作者ID，与上方wp:author_id一致】]]></wp:meta_value>
		</wp:postmeta>
	</item>

</channel>
</rss>