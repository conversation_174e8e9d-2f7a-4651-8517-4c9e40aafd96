# API接口文档


## 1. 聊天控制器 (ChatController)
- 基础路径: `/chat`

### 1.1 获取当前模型
- 请求方法: `GET`
- 请求路径: `/getModel`
- 返回类型: `Mono<Result<String>>`
- 描述: 获取当前使用的AI模型

### 1.2 普通对话
- 请求方法: `POST`
- 请求路径: `/call`
- 请求参数:
  - `message` (可选): 对话消息，默认值为`DEFAULT_USER_MESSAGE_PROMPT`
  - `sessionId` (可选): 会话ID
- 返回类型: `Result<String>`
- 描述: 发送普通文本消息并获取响应

### 1.3 流式对话
- 请求方法: `POST`
- 请求路径: `/stream/{streamId}`
- 产生类型: `MediaType.TEXT_EVENT_STREAM_VALUE`
- 请求参数:
  - `streamId` (路径参数): 流ID
  - `message` (可选): 对话消息，默认值为`DEFAULT_USER_MESSAGE_PROMPT`
  - `sessionId` (可选): 会话ID
- 返回类型: `Flux<ServerSentEvent<String>>`
- 描述: 以流式方式发送消息并获取响应

### 1.4 终止流式输出
- 请求方法: `DELETE`
- 请求路径: `/stopStream/{streamId}`
- 请求参数:
  - `streamId` (路径参数): 流ID
- 返回类型: `Result<String>`
- 描述: 终止指定的流式输出

## 2. 聊天消息控制器 (ChatMessageController)
- 基础路径: `/chatMessage`

### 2.1 获取消息记录
- 请求方法: `GET`
- 请求路径: `/getAllMessages/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
- 返回类型: `Result<List<ChatMessage>>`
- 描述: 获取指定会话的所有消息记录

### 2.2 获取最新消息
- 请求方法: `GET`
- 请求路径: `/getLatestMessage/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
  - `limit` (可选): 获取消息数量，默认值为10
- 返回类型: `Result<List<ChatMessage>>`
- 描述: 获取指定会话的最新N条消息

### 2.3 获取消息数量
- 请求方法: `GET`
- 请求路径: `/getMessageCount/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
- 返回类型: `Result<Long>`
- 描述: 获取指定会话的消息数量

### 2.4 删除消息
- 请求方法: `DELETE`
- 请求路径: `/deleteMessages/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
- 返回类型: `Result<String>`
- 描述: 删除指定会话的所有消息

## 3. 会话控制器 (ChatSessionController)
- 基础路径: `/chatSession`

### 3.1 新建会话
- 请求方法: `POST`
- 请求路径: `/createSession`
- 请求参数:
  - `message` (可选): 初始消息，默认值为`DEFAULT_USER_MESSAGE_PROMPT`
- 返回类型: `Result<String>`
- 描述: 创建新的会话

### 3.2 删除会话
- 请求方法: `DELETE`
- 请求路径: `/deleteSession/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
- 返回类型: `Result<String>`
- 描述: 删除指定会话

### 3.3 获取会话列表
- 请求方法: `GET`
- 请求路径: `/getChatSessionByUserId`
- 返回类型: `Result<List<ChatSession>>`
- 描述: 获取当前用户的所有会话列表

## 4. 导出控制器 (ExportController)
- 基础路径: `/export`

### 4.1 导出会话标题为TXT
- 请求方法: `GET`
- 请求路径: `/exportSessionNameTxt`
- 返回类型: `Result<String>`
- 描述: 导出当前用户的所有会话标题为TXT格式

### 4.2 导出会话为HTML
- 请求方法: `GET`
- 请求路径: `/exportSessionHtml/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
- 返回类型: `Result<String>`
- 描述: 导出指定会话为HTML格式

### 4.3 导出会话为XML
- 请求方法: `GET`
- 请求路径: `/exportSessionXml/{sessionId}`
- 请求参数:
  - `sessionId` (路径参数): 会话ID
  - `category` (必需): 分类
- 返回类型: `Result<String>`
- 描述: 导出指定会话为XML格式（适配WordPress语法）

## 5. HTML美化控制器 (HtmlBeautifulController)
- 基础路径: `/beautiful`

### 5.1 美化HTML
- 请求方法: `POST`
- 请求路径: `/beautifyHtml`
- 返回类型: `Result<String>`
- 描述: 批量美化HTML内容

## 6. 多模态控制器 (MultimodalController)
- 基础路径: `/multiModel`

### 6.1 图像分析
- 请求方法: `POST`
- 请求路径: `/imageAnalyze/{filename}`
- 请求参数:
  - `filename` (路径参数): 图像文件名
- 返回类型: `Result<String>`
- 描述: 分析已上传的图像文件

### 6.2 文本分析
- 请求方法: `POST`
- 请求路径: `/textAnalyze/{filename}`
- 请求参数:
  - `filename` (路径参数): 文本文件名
- 返回类型: `Result<String>`
- 描述: 分析已上传的文本文件

## 7. 图文控制器 (TextImageController)
- 基础路径: `/textImage`

### 7.1 生成图像
- 请求方法: `POST`
- 请求路径: `/imageGenerate`
- 请求参数:
  - `message` (可选): 图像描述，默认值为`DEFAULT_SYSTEM_MESSAGE_PROMPT_TEXT_IMAGE`
  - `quality` (可选): 图像质量，默认值为`DEFAULT_IMAGE_QUALITY`
- 返回类型: `String` (直接返回HTML img标签)
- 描述: 根据文本描述生成图像

## 8. 上传控制器 (UploadController)
- 基础路径: `/upload`

### 8.1 上传图片
- 请求方法: `POST`
- 请求路径: `/image`
- 请求参数:
  - `file` (必需): 图片文件
- 返回类型: `Result<String>`
- 描述: 上传图片文件

### 8.2 上传文本
- 请求方法: `POST`
- 请求路径: `/text`
- 请求参数:
  - `file` (必需): 文本文件
- 返回类型: `Result<String>`
- 描述: 上传文本文件

## 9. 用户控制器 (UserController)
- 基础路径: `/user`

### 9.1 注册
- 请求方法: `POST`
- 请求路径: `/register`
- 请求体: `UserDto` (包含用户注册信息)
- 返回类型: `Result<String>`
- 描述: 用户注册

### 9.2 登录
- 请求方法: `POST`
- 请求路径: `/login`
- 请求体: `UserDto` (包含用户登录信息)
- 返回类型: `Result<String>` (返回token)
- 描述: 用户登录

### 9.3 退出
- 请求方法: `POST`
- 请求路径: `/quit`
- 返回类型: `Result<String>`
- 描述: 退出登录，使当前token失效

### 9.4 注销
- 请求方法: `DELETE`
- 请求路径: `/logout`
- 返回类型: `Result<String>`
- 描述: 注销账号，使当前token失效并清除用户数据

## 通用说明
1. 所有接口均需要通过`HttpServletRequest`获取用户ID，前端无需额外传递用户标识
2. 除特别说明外，所有接口返回`Result`对象，包含成功/失败状态及数据
3. `Result`对象结构:
   - `success`: 布尔值，表示请求是否成功
   - `code`: 状态码
   - `message`: 消息描述
   - `data`: 业务数据
