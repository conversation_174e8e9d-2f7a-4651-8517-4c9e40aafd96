<?xml version="1.0" encoding="UTF-8"?>
<!-- 
  RSS根标签
  - version="2.0"：使用RSS 2.0版本规范
  - 命名空间声明（xmlns:xxx）：扩展RSS功能的命名空间
    - excerpt：WordPress导出的摘要相关命名空间
    - content：内容模块命名空间（用于定义文章完整内容）
    - wfw：评论API相关命名空间
    - dc：Dublin Core元数据标准（用于作者、日期等元信息）
    - wp：WordPress专用扩展命名空间（用于WordPress特有的数据）
-->
<rss version="2.0"
     xmlns:excerpt="https://wordpress.org/export/1.2/excerpt/"
     xmlns:content="https://purl.org/rss/1.0/modules/content/"
     xmlns:wfw="https://wellformedweb.org/CommentAPI/"
     xmlns:dc="https://purl.org/dc/elements/1.1/"
     xmlns:wp="https://wordpress.org/export/1.2/">
  
  <!-- 频道信息（整个站点的核心信息） -->
  <channel>
    <title>127.0.0.1</title> <!-- 站点标题 -->
    <link>https://127.0.0.1</link> <!-- 站点首页链接 -->
    <description>127.0.0.1</description> <!-- 站点描述（与标题一致） -->
    <pubDate> </pubDate>
    <language>zh-CN</language> 
    
    <!-- WordPress扩展信息 -->
    <wp:wxr_version>1.2</wp:wxr_version> <!-- WordPress导出格式版本（WXR 1.2） -->
    <wp:base_site_url>https://127.0.0.1</wp:base_site_url> <!-- 基础站点URL（整个站点的根地址） -->
    <wp:base_blog_url>https://127.0.0.1</wp:base_blog_url> <!-- 博客基础URL（当前博客的根地址，此处与站点URL一致） -->

    <!-- 作者信息模块（WordPress特有的作者数据） -->
    <wp:author>
      <wp:author_id>2940634257</wp:author_id> <!-- 作者唯一ID（系统内部标识） -->
      <wp:author_login>2940634257</wp:author_login> <!-- 作者登录账号 -->
      <wp:author_email><EMAIL></wp:author_email> <!-- 作者邮箱 -->
      <wp:author_display_name><![CDATA[Lillian]]></wp:author_display_name> <!-- 作者显示名称（前端展示用） -->
      <wp:author_first_name><![CDATA[]]></wp:author_first_name> <!-- 作者名 -->
      <wp:author_last_name><![CDATA[]]></wp:author_last_name> <!-- 作者姓氏 -->
    </wp:author>

    <!-- 分类信息模块 -->
    	<!-- 已经在wp中定义了：百度，搜狗，谷歌，雅虎 -->


    <!-- 文章内容模块（单个文章的数据） -->
    <item>
      <title> </title> <!-- 文章标题 -->
      <link>https://127.0.0.1/%category%/%year%%monthnum%%day%/%postname%.html</link> <!-- 文章链接（URL模板，实际访问时替换变量） -->
      <pubDate> </pubDate> <!-- 文章发布时间 -->
      <dc:creator><![CDATA[Lillian]]></dc:creator> <!-- 文章作者（关联上方的作者信息） -->
      <guid isPermaLink="false">https://127.0.0.1/?p=1001</guid> <!-- 文章唯一标识（非永久链接，用于系统识别） -->
      <description><![CDATA[]]></description> <!-- 文章摘要（此处为空） -->
      
      <!-- 文章完整内容（CDATA标记内为原始内容，避免XML解析冲突） -->
      <content:encoded><![CDATA[
        <h3></h3>
        .......
        - <strong></strong>
      ]]></content:encoded>
      
      <excerpt:encoded><![CDATA[]]></excerpt:encoded> <!-- 文章扩展摘要（WordPress专用，此处为空） -->
      
      <!-- WordPress文章特有属性 -->
      <wp:post_id>1001</wp:post_id> <!-- 文章ID（系统内部唯一标识） -->
      <wp:post_date>2025-07-18 00:00:00</wp:post_date> <!-- 文章发布时间（本地时间，北京时间） -->
      <wp:post_date_gmt>2025-07-17 16:00:00</wp:post_date_gmt> <!-- 文章发布时间（GMT时区，比北京时间晚8小时） -->
      <wp:post_modified>2025-07-18 00:00:00</wp:post_modified> <!-- 文章最后修改时间（本地时间） -->
      <wp:post_modified_gmt>2025-07-17 16:00:00</wp:post_modified_gmt> <!-- 文章最后修改时间（GMT时区） -->
      <wp:post_status>publish</wp:post_status> <!-- 文章状态（publish表示已发布） -->
      <wp:post_type>post</wp:post_type> <!-- 内容类型（post表示文章，其他类型如page为页面） -->
      <wp:post_password> </wp:post_password> <!-- 文章访问密码（此处为空，无需密码） -->
      <wp:post_name> </wp:post_name> <!-- 文章别名（用于URL生成"） -->
      <wp:status>publish</wp:status> <!-- 文章状态重复声明（与post_status一致） -->
      <wp:post_parent>0</wp:post_parent> <!-- 父文章ID（0表示无父文章，非子页面） -->
      <wp:menu_order>0</wp:menu_order> <!-- 排序权重（用于前端显示顺序，0为默认） -->
      <wp:post_type>post</wp:post_type> <!-- 内容类型重复声明 -->
      <wp:post_mime_type> </wp:post_mime_type> <!-- 文章MIME类型（普通文章无需设置，此处为空） -->
      <wp:comment_status>open</wp:comment_status> <!-- 评论状态（open表示允许评论） -->
      <wp:pings_status>open</wp:pings_status> <!-- 引用通知状态（open表示允许其他站点引用通知） -->
      <wp:post_thumbnail>0</wp:post_thumbnail> <!-- 文章缩略图ID（0表示无缩略图） -->
      
      <!-- 文章所属分类 -->
      <category domain="category" nicename=" "><![CDATA[]]></category> <!-- 分类信息：别名""，分类名称 -->
      
      <!-- 文章元数据（WordPress自定义字段） -->
      <wp:postmeta>
        <wp:meta_key>_edit_last</wp:meta_key> <!-- 元数据键（最后编辑者标识） -->
        <wp:meta_value>2940634257</wp:meta_value> <!-- 元数据值（关联作者ID，表示最后编辑者为该作者） -->
      </wp:postmeta>
    </item>
  </channel>
</rss>