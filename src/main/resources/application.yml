server:
  port: 8080
spring:
  application:
    name: ollama
#ollama
  ai:
    ollama:
      base-url: http://localhost:11434
      chat:
#        model: qwen:1.8b
        model: gemma3:1b
#        model: llama3.2:3b
#        model: deepseek-r1:1.5b
#        model: deepseek-r1:7b
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: https://ai.nengyongai.cn/
      #chat 对话模型暂未使用
      #image 图片模型
      image:
        options:
          model: dall-e-3
  #mysql
  datasource:
    url: ****************************************************************************
    username: root
    password: ${MYSQL_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
#文件上传
  servlet:
    multipart:
      max-file-size: 1MB #限制单个文件上传的最大大小
      max-request-size: 10MB #限制整个请求（含多个文件、表单数据等） 的最大大小
#静态资源映射 - 禁用默认配置，使用自定义StaticResourceConfig
  web:
    resources:
      add-mappings: false
#MyBatis-Plus
mybatis-plus:
  type-aliases-package: com.cjx.ollama.pojo.entity #实体类 路径
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true #驼峰命名自动映射
#日志 trace < debug < info < warn < error
logging:
  level:
    org.springframework.ai.chat.client.advisor: error
    com.qhf.ollama: error
