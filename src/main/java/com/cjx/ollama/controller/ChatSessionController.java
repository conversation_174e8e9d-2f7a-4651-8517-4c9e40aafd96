package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatSessionService;
import com.cjx.ollama.utils.context.UserContextUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 会话控制器 - 只负责HTTP请求处理和参数校验
 * Author: cjx
 * Date: 2025/6/30
 */
@Slf4j
@RestController
@RequestMapping("/chatSession")
@RequiredArgsConstructor
public class ChatSessionController {

    private final ChatSessionService chatSessionService;

    /**
     * 新建会话
     */
    @PostMapping("/insertSession")
    public Result<String> insertSession(
            HttpServletRequest request,
            @RequestParam(value = "message", required = false) String message
    ) {
            // 获取用户ID
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            // 调用业务服务
            String sessionId = chatSessionService.insertSession(userId, message);
            return Result.success(sessionId);
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/deleteSession/{sessionId}")
    public Result<String> deleteSession(
            @PathVariable String sessionId
    ) {
        log.info("删除会话，sessionId: {}", sessionId);

            // 校验参数
            ValidationUtil.validateSessionId(sessionId);
            // 调用业务服务
            chatSessionService.deleteSession(sessionId);
            return Result.success("删除成功，sessionId: " + sessionId);
    }

    /**
     * 获取当前用户的会话列表
     */
    @GetMapping("/getChatSessionByUserId")
    public Result<List<ChatSession>> getChatSessionByUserId(
            HttpServletRequest request
    ) {
        log.info("获取用户会话列表");

            // 获取用户ID
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            // 调用业务服务
            List<ChatSession> sessionList = chatSessionService.getChatSessionByUserId(userId);
            if (sessionList.isEmpty()) {
                return  Result.error(ResultEnum.SESSION_NOT_FOUND);
            }
            return Result.success(sessionList);

    }




}

