package com.cjx.ollama.controller;

import com.cjx.ollama.result.Result;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ExportService;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * Author: cjx
 * Date: 2025/7/18
 * 导出会话标题txt
 * 导出会话为html
 * 导出会话为xml（适配wordpress语法）
 */
@RestController
@RequestMapping("export")
@RequiredArgsConstructor
public class ExportController {
    private final ExportService exportService;

    /**
     * 导出会话标题
     * txt
     * 当前用户
     */
    @GetMapping(value = "/exportSessionNameTxt")
    public Result<String> exportSessionNameTxt(
            HttpServletRequest request
    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String txt = exportService.exportSessionNameTxt(userId);
        if (txt.isEmpty()) {
            return Result.error(ResultEnum.SESSION_LIST_EMPTY);
        }
        return Result.success(txt);
    }

    /**
     * 导出会话
     * html
     * 当前用户
     */
    @GetMapping(value = "/exportSessionHtml/{sessionId}")
    public Result<String> exportSessionHtml(
            HttpServletRequest request,
            @PathVariable String sessionId,
            @RequestParam String imageCategory

    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String html = exportService.exportSessionHtml(userId,sessionId,imageCategory);
        return Result.success(html);
    }

    /**
     * 导出会话
     * xml
     * 当前用户
     */
    @GetMapping(value = "/exportSessionXml/{sessionId}")
    public Result<String> exportSessionXml(
            HttpServletRequest request,
            @PathVariable  String sessionId,
            @RequestParam String postCategory,
            @RequestParam String imageCategory

    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String xml = exportService.exportSessionXml(userId,sessionId,postCategory,imageCategory);
        return Result.success(xml);
    }

    /**
     * 导出会话
     * excel
     * 当前用户
     */
    @GetMapping(value = "/exportSessionExcel/{sessionId}")
    public Result<String> exportSessionExcel(
            HttpServletRequest request,
            @PathVariable  String sessionId,
            @RequestParam String postCategory,
            @RequestParam String imageCategory
    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String excel = exportService.exportSessionExcel(userId,sessionId,postCategory,imageCategory);
        return Result.success(excel);
    }

    /**
     * 导出会话
     * csv
     * 当前用户
     */
    @GetMapping(value = "/exportSessionCsv/{sessionId}")
    public Result<String> exportSessionCsv(
            HttpServletRequest request,
            @PathVariable  String sessionId,
            @RequestParam String postCategory,
            @RequestParam String imageCategory


    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String csv = exportService.exportSessionCsv(userId,sessionId,postCategory,imageCategory);
        return Result.success(csv);
    }

}
