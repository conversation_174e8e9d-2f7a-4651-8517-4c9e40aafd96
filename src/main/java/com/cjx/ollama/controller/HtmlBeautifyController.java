package com.cjx.ollama.controller;

import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.HtmlBeautifyService;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * HTML美化控制器
 * 批量美化
 * Author: cjx
 * Date: 2025/7/3
 */
@Slf4j
@RestController
@RequestMapping("/beautify")
@RequiredArgsConstructor
public class HtmlBeautifyController {


    private final HtmlBeautifyService htmlBeautifyService;

    @PostMapping("/beautifyHtml")
    public Result<String> beautifyHtml(
            HttpServletRequest request
    ) {
        Long userId = UserContextUtil.getUserIdFromRequest(request);
            String resultMessage = htmlBeautifyService.beautifyHtml(userId);
            return Result.success(resultMessage);
    }
}