package com.cjx.ollama.controller;

import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.UploadService;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * 文件上传控制器
 * Author: cjx
 * Date: 2025/7/3
 */
@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class UploadController {


    private final UploadService uploadService;

    /**
     * 图片上传
     */
    @PostMapping("/image")
    public Result<String> uploadImage(
            HttpServletRequest request,
            @RequestParam("file") MultipartFile file
    ) {
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            String fileUrl = uploadService.uploadImage(userId,file);
            return Result.success(fileUrl);
    }

    /**
     * 文本文件上传
     */
    @PostMapping("/text")
    public Result<String> uploadText(
            HttpServletRequest request,
            @RequestParam("file") MultipartFile file
    ) {
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            String fileUrl = uploadService.uploadText(userId,file);
            return Result.success(fileUrl);
    }




}

