package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.entity.TextImage;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.TextImageService;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


import java.util.List;

import static com.cjx.ollama.utils.constant.Parameter.DEFAULT_IMAGE_QUALITY;

/**
 * 图像生成控制器
 * Author: cjx
 * Date: 2025/7/3
 */
@Slf4j
@RestController
@RequestMapping("/textImage")
@RequiredArgsConstructor
public class TextImageController {

    private final TextImageService textImageService;

    /**
     * 图像生成接口
     */

    //   http://localhost:8080/textImage/imageGenerate?message=xxxxx&quality
    @PostMapping("/imageGenerate")
    public Result<String> imageGenerate(
            HttpServletRequest request,
            @RequestParam(value = "message") String message,
            @RequestParam(value = "quality",defaultValue = DEFAULT_IMAGE_QUALITY) String quality
    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        String url =  textImageService.generateImage(userId, message, quality);
        return Result.success(url);
    }

    /**
     * 查询当前用户图像生成历史
     */
    @GetMapping("/getImageGenerateByUserId")
    public Result<List<TextImage>> getImageGenerateByUserId(
            HttpServletRequest request
    ) {
        Long userId = UserContextUtil.getUserIdFromRequest(request);
      //调用业务服务
        List<TextImage> textImageList = textImageService.getImageGenerateByUserId(userId);
        return Result.success(textImageList);
    }

}
