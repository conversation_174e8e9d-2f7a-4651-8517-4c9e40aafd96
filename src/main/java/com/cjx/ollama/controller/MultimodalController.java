package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.entity.Multimodal;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.MultimodalService;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 多模态AI分析控制器 - 只负责HTTP请求处理和参数校验
 * 提供图像和文本的AI识别与分析接口。
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/multimodal")
@RequiredArgsConstructor
public class MultimodalController {
    
    private final MultimodalService multimodalService;

    /**
     * 图像分析 - 基于已上传的图像文件
     */
    @PostMapping("/imageAnalyze/{filename}")
    public Result<String> imageAnalyze(
            HttpServletRequest request,
            @PathVariable String filename
    ) {
             Long userId = UserContextUtil.getUserIdFromRequest(request);
            // 调用业务服务
            String result = multimodalService.imageAnalyze(userId,filename);
            return Result.success(result);

    }


    /**
     * 文本分析 - 基于已上传的文本文件
     */
    @PostMapping("/textAnalyze/{filename}")
    public Result<String> textAnalyze(
            HttpServletRequest request,
            @PathVariable String filename
    ) {
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            // 调用业务服务
            String result = multimodalService.textAnalyze(userId,filename);
            return Result.success(result);
    }

    /**
     * 多模态列表
     */
    @GetMapping("/getMultimodelByUserId")
    public Result<List<Multimodal>> getMultimodelByUserId(
            HttpServletRequest request
    ) {
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        List<Multimodal> multimodalList = multimodalService.getMultimodelByUserId(userId);
        return Result.success(multimodalList);
    }

}
