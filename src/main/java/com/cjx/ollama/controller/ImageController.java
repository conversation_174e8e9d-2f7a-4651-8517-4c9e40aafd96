package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.dto.BaseQueryDto;
import com.cjx.ollama.pojo.entity.Image;
import com.cjx.ollama.result.PageResult;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.ImageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/25
 */
@RestController
@RequestMapping("/image")
@RequiredArgsConstructor
public class ImageController {
    private final ImageService imageService;

    /**
     * 查询图片
     * 待优化:补充分页参数
     */
    @GetMapping("/getImageList")
    public Result<PageResult<Image>> getImageList(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String description,
            BaseQueryDto baseQueryDto
    ){

        //调用业务服务获取图片
        PageResult<Image> imageList =imageService.getImageList(category,description,baseQueryDto);
        return Result.success(imageList);
    }

    /**
     * 新增图片
     * 链接
     */
    @PostMapping("/insertImage")
    public Result<Image> insertImage(
            @RequestBody Image image
    ){

        //调用业务服务获取图片
        Image insertImage =imageService.insertImage(image);
        return Result.success(insertImage);
    }

    /**
     * 删除图片
     */
    @DeleteMapping("/deleteImage/{imageId}")
    public Result<String> deleteImage(
            @PathVariable Long  imageId
    ){

        //调用业务服务获取图片
        imageService.deleteImage(imageId);
        return Result.success("删除成功，ImageId："+imageId);
    }

    /**
     * 更新图片
     */
    @PostMapping("/updateImage")
    public Result<Image> updateImage(
            @RequestBody Image image
    ){

        //调用业务服务获取图片
        Image updateImage =imageService.updateImage(image);
        return Result.success(updateImage);
    }

    /**
     * 批量删除图片
     * 接收前端传递的ImageId列表（JSON格式）
     * 批量上传图片的时候将图片存到了Image目录，batchDeleteImages需要同步删除目录下与数据库能对应上的图片
     */
    @DeleteMapping("/batchDeleteImages")
    public Result<String> batchDeleteImages(
            @RequestBody List<Long> imageIds // 接收JSON数组：[1,2,3]
    ) {
        // 调用服务层批量删除方法
        imageService.batchDeleteImages(imageIds);
        return Result.success("批量删除成功，共删除 " + imageIds.size() + " 条图片");
    }

    /**
     * 批量导入图片链接
     * 支持excel
     */
    @PostMapping("/importImagesByExcel")
    public Result<List<Image>> importImagesByExcel(
            @RequestParam("excel") MultipartFile excel
    ) {
        List<Image> imageList = imageService.importImagesByExcel(excel);
        return Result.success(imageList);
    }

    /**
     * 批量上传图片
     * 保存链接到数据库
     */
    @PostMapping("/batchUploadImages")
    public Result<List<String>> batchUploadImages(
            @RequestParam("category") String category,
            @RequestParam("description") String description,
            @RequestParam("files") MultipartFile[] files
    ) {
        List<String> fileUrls = imageService.batchUploadImages(category,description, files);
        return Result.success(fileUrls);
    }
}
