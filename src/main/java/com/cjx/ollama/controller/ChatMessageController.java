package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.ChatMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/1
 */
@Slf4j
@RestController
@RequestMapping("/chatMessage")
@RequiredArgsConstructor
public class ChatMessageController {

    private final ChatMessageService chatMessageService;

    /**
     * 获取消息记录
     */
    @GetMapping("/getMessageList/{sessionId}")
    public Result<List<ChatMessage>> getMessageList(
            @PathVariable String sessionId
    ) {
            // 调用业务服务
            List<ChatMessage> messages = chatMessageService.getMessageList(sessionId);
            return Result.success(messages);
    }

    /**
     * 获取指定会话的最新N条消息
     */
    @GetMapping("/getLatestMessages/{sessionId}")
    public Result<List<ChatMessage>> getLatestMessages(
            @PathVariable String sessionId,
            @RequestParam(defaultValue = "10") Integer limit
    ) {
            // 调用业务服务
            List<ChatMessage> messages = chatMessageService.getLatestMessages(sessionId, limit);
            return Result.success(messages);
    }

    /**
     * 获取指定会话的消息数量
     */
    @GetMapping("/getMessageCount/{sessionId}")
    public Result<Long> getMessageCount(
            @PathVariable String sessionId
    ) {
            // 调用业务服务
            Long count = chatMessageService.getMessageCount(sessionId);
            return Result.success(count);

    }

    /**
     * 删除指定会话的所有消息
     */
    @DeleteMapping("/deleteMessages/{sessionId}")
    public Result<String> deleteMessages(
            @PathVariable String sessionId
    ) {
            // 调用业务服务
            Long deletedCount = chatMessageService.deleteMessages(sessionId);
            return Result.success("删除成功，共删除 " + deletedCount + " 条消息");
    }
}
