package com.cjx.ollama.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 静态图片访问控制器 - 处理中文路径的图片访问
 * Author: cjx
 * Date: 2025/7/30
 */
@Slf4j
@RestController
public class StaticImageController {

    /**
     * 处理 /static-image/** 路径的图片访问
     */
    @GetMapping("/static-image/**")
    public ResponseEntity<Resource> getStaticImage(jakarta.servlet.http.HttpServletRequest request) {
        try {
            // 获取完整的请求路径
            String fullPath = request.getRequestURI();
            log.info("=== 静态图片请求开始 ===");
            log.info("原始请求路径: {}", fullPath);

            // 移除 /static-image 前缀，获取相对路径
            String relativePath = fullPath.substring("/static-image/".length());
            log.info("移除前缀后的路径: {}", relativePath);

            // URL解码，处理中文路径
            String decodedPath = URLDecoder.decode(relativePath, StandardCharsets.UTF_8);
            log.info("URL解码后的路径: {}", decodedPath);

            // 构建完整的文件路径
            String basePath = System.getProperty("user.dir");
            log.info("项目根目录: {}", basePath);

            File imageFile = new File(basePath, "image/" + decodedPath);
            log.info("完整文件路径: {}", imageFile.getAbsolutePath());
            log.info("文件是否存在: {}", imageFile.exists());
            log.info("是否为文件: {}", imageFile.isFile());

            if (!imageFile.exists()) {
                log.error("文件不存在: {}", imageFile.getAbsolutePath());
                // 列出父目录的内容
                File parentDir = imageFile.getParentFile();
                if (parentDir.exists()) {
                    log.info("父目录内容:");
                    File[] files = parentDir.listFiles();
                    if (files != null) {
                        for (File f : files) {
                            log.info("  - {}", f.getName());
                        }
                    }
                }
                return ResponseEntity.notFound().build();
            }

            if (!imageFile.isFile()) {
                log.error("路径不是文件: {}", imageFile.getAbsolutePath());
                return ResponseEntity.notFound().build();
            }
            
            // 创建资源
            Resource resource = new FileSystemResource(imageFile);
            
            // 确定文件类型
            String contentType = getContentType(imageFile.getName());
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, contentType)
                    .header(HttpHeaders.CACHE_CONTROL, "max-age=3600")
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("获取静态图片失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据文件扩展名确定Content-Type
     */
    private String getContentType(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        return switch (extension) {
            case "jpg", "jpeg" -> MediaType.IMAGE_JPEG_VALUE;
            case "png" -> MediaType.IMAGE_PNG_VALUE;
            case "gif" -> MediaType.IMAGE_GIF_VALUE;
            case "webp" -> "image/webp";
            default -> MediaType.APPLICATION_OCTET_STREAM_VALUE;
        };
    }
}
