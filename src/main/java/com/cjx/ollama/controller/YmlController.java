package com.cjx.ollama.controller;

import com.cjx.ollama.result.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * Author: cjx
 * Date: 2025/7/23
 */
@RestController
@RequestMapping("/yml")
public class YmlController {
    @Value("${spring.ai.openai.api-key}")
    private String apiKey;
    @Value("${spring.ai.ollama.chat.model}")
    private String model;
    @Value("${spring.datasource.password}")
    private String dbPassword;

    @GetMapping("/apiKey")
    public String apiKey() {
        return "密钥长度：" + apiKey.length();  // 仅打印长度，避免暴露
    }
    @GetMapping("/getModel")
    public Result<String> getModel() {
        return Result.success(model);
    }
    @GetMapping("/dbPassword")
    public String dbPassword() {
        return "密码长度：" + dbPassword.length();  // 仅打印长度，避免暴露密码
    }
}
