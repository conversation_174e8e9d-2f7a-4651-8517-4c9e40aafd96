package com.cjx.ollama.controller;

import com.cjx.ollama.result.Result;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatService;
import com.cjx.ollama.utils.context.UserContextUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.cjx.ollama.utils.constant.Prompt.DEFAULT_USER_MESSAGE_PROMPT;

/**
 * 聊天控制器 - 只负责HTTP请求处理和参数校验
 * Author: cjx
 * Date: 2025/6/25
 */

@Slf4j
@RestController
@RequestMapping("/chat")
@RequiredArgsConstructor
public class ChatController {

    private final ChatService chatService;

    /**
     * 查询当前模型
     * model: deepseek-r1:1.5b
     */
    @GetMapping("getModel")
    public Mono<Result<String>> getModel() {
        return Result.success(chatService.getModel()).getData();
    }

    /**
     * 普通对话
     */
    @PostMapping("/call")
    public Result<String> call(
            HttpServletRequest request,
            @RequestParam(value = "message", defaultValue = DEFAULT_USER_MESSAGE_PROMPT) String message,
            @RequestParam(required = false) String sessionId
    ) {
            // 获取用户ID
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            // 调用业务服务
            String response = chatService.chatCall(userId, message, sessionId);
            return Result.success(response);
    }

    /**
     * 流式对话
     */
    @PostMapping(value = "/stream/{streamId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> stream(
            HttpServletRequest request,
            //RequestParam：可选操作参数
            //PathVariable：资源标识符
            @RequestParam(value = "message", defaultValue = DEFAULT_USER_MESSAGE_PROMPT) String message,
            @RequestParam(value = "sessionId", required = false) String sessionId,
            @PathVariable String streamId
    ) {
            // 校验参数
            ValidationUtil.validateStreamId(streamId);
            // 获取用户ID
            Long userId = UserContextUtil.getUserIdFromRequest(request);
            // 调用业务服务
            return chatService.chatStream(userId, message, sessionId, streamId);
    }

    /**
     * 终止流式输出
     */
    @DeleteMapping("/stopStream/{streamId}")
    public Result<String> stopStream(
            @PathVariable String streamId
    ) {
        boolean success = chatService.stopStream(streamId);
        if (success) {
            return Result.success("流已终止"+streamId);
        } else {
            return Result.error(ResultEnum.STREAM_NOT_FOUND);
        }
    }


}
