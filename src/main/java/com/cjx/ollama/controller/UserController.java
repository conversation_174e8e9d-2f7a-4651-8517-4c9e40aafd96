package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.dto.UserDto;
import com.cjx.ollama.pojo.entity.User;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.UserService;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器 - 只负责HTTP请求处理和参数校验
 * Author: cjx
 * Date: 2025/7/4
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;

    /**
     * 注册(也是新增)
     */
    @PostMapping("/register")
    public Result<String> register(
            @RequestBody UserDto userDto
    ) {
            // 调用业务服务
            String result = userService.register(userDto);
            return Result.success(result);
    }

    /**
     * 登录
     */
    @PostMapping("/login")
    public Result<String> login(
            @RequestBody UserDto userDto
    ) {
            // 调用业务服务
            String token = userService.login(userDto);
            return Result.success(token);
            

    }
    /**
     * 退出 - 使当前token失效
     */
    @PostMapping("/quit")
    public Result<String> quit(
            HttpServletRequest request
    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String result = userService.quit(userId);
        return Result.success(result);
    }
    
    /**
     * 注销 - 使当前token失效，并清除用户数据
     * 清除用户数据：
     * 1. 删除用户的会话
     * 2. 删除用户的消息
     * 3. 删除用户的多模态分析记录
     * 4. 删除用户的文生图记录
     * 5. 删除用户上传的文件
     * 6. 删除用户账号
     * 1-4：表中存在user_id的通过外键级联更新保证事务的一致性
     * 5：删除用户上传的文件 需要处理
     *
     */
    @DeleteMapping("/logout")
    public Result<String> logout(
            HttpServletRequest request
    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        String result = userService.logout(userId);
        return Result.success(result);
    }

    /**
     * 获取用户信息接口
     */
    @GetMapping("/getUserInfo")
    public Result<User> getUserInfo(
            HttpServletRequest request
    ) {
        // 获取用户ID
        Long userId = UserContextUtil.getUserIdFromRequest(request);
        // 调用业务服务
        User user = userService.getUserInfo(userId);
        return Result.success(user);
    }
    /**
     * 用户列表
     */
    @GetMapping("/getUserList")
    public Result<List<User>> getUserList() {
        // 调用业务服务
        List<User> userList = userService.getUserList();
        return Result.success(userList);
    }
    /**
     * 更新用户
     */
    @PostMapping("/updateUser")
    public Result<User> updateUser(
            @RequestBody User user
    ) {
        // 调用业务服务
        userService.updateUser(user);
        return Result.success(user);
    }
    /**
     * 删除用户
     * 彻底物理删除用户账号及所有关联数据
     * 不依赖当前登录状态
     */
    @DeleteMapping("/deleteUser/{userId}")
    public Result<String> deleteUser(
            @PathVariable Long userId
    ) {
        // 调用业务服务
        userService.deleteUser(userId);
        return Result.success("删除成功，userId: " + userId);
    }


}