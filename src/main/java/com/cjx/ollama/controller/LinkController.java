package com.cjx.ollama.controller;

import com.cjx.ollama.pojo.dto.BaseQueryDto;
import com.cjx.ollama.pojo.entity.Link;
import com.cjx.ollama.result.PageResult;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.service.LinkService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/24
 */
@RestController
@RequestMapping("/link")
@RequiredArgsConstructor
public class LinkController {
    private final LinkService linkService;

    /**
     * 查询链接
     * 待优化:补充分页参数
     */
    @GetMapping("/getLinkList")
    public Result<PageResult<Link>> getLinkList(
            @RequestParam(required = false) String keyWord,
            @RequestParam(required = false) String category,
            BaseQueryDto baseQueryDto
    ){
        //调用业务服务获取链接
        PageResult<Link> pageResult =linkService.getLinkList(keyWord,category,baseQueryDto);
        return Result.success(pageResult);
    }

    /**
     * 新增链接
     */
    @PostMapping("/insertLink")
    public Result<Link> insertLink(
            @RequestBody Link link
    ){

        //调用业务服务获取链接
        Link insertLink =linkService.insertLink(link);
        return Result.success(insertLink);
    }

    /**
     * 删除链接
     */
    @DeleteMapping("/deleteLink/{linkId}")
    public Result<String> deleteLink(
            @PathVariable Long  linkId
    ){

        //调用业务服务获取链接
         linkService.deleteLink(linkId);
       return Result.success("删除成功，linkId："+linkId);
    }

    /**
     * 更新链接
     */
    @PostMapping("/updateLink")
    public Result<Link> updateLink(
            @RequestBody Link link
    ){

        //调用业务服务获取链接
        Link updateLink =linkService.updateLink(link);
        return Result.success(updateLink);
    }

    /**
     * 批量删除链接
     * 接收前端传递的linkId列表（JSON格式）
     */
    @DeleteMapping("/batchDeleteLinks")
    public Result<String> batchDeleteLinks(
            @RequestBody List<Long> linkIds // 接收JSON数组：[1,2,3]
    ) {
        // 调用服务层批量删除方法
        linkService.batchDeleteLinks(linkIds);
        return Result.success("批量删除成功，共删除 " + linkIds.size() + " 条链接");
    }

    /**
     * 批量导入
     * 支持excel
     */
    @PostMapping("/importLinksByExcel")
    public Result<List<Link>> importLinksByExcel(
            @RequestParam("excel") MultipartFile excel
    ) {
        List<Link> linkList = linkService.importLinksByExcel(excel);
        return Result.success(linkList);
    }
}
