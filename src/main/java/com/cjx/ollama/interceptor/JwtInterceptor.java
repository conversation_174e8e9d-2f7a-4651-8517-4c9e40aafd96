package com.cjx.ollama.interceptor;

import com.cjx.ollama.result.Result;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.utils.jwt.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.security.SignatureException;
import java.io.IOException;

import static com.cjx.ollama.result.ResultEnum.*;

@Slf4j
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull Object handler
    ) throws Exception {

        // 1. OPTIONS 请求直接放行（跨域预检）
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }

        // 2. 静态资源GET请求直接放行
        if (isStaticResourceGetRequest(request)) {
            return true;
        }

        // 3. 提取token
        String token = extractTokenFromRequest(request);

        // 4. 验证token是否存在
        if (isTokenEmpty(token)) {
            log.warn("请求[{} {}]未携带有效 token（Authorization 需为 Bearer <token>，或 token 头需非空）",
                    request.getMethod(), request.getRequestURI());
            sendUnauthorizedResponse(response, TOKEN_NULL);
            return false;
        }

        // 5. 检查token是否在黑名单
        if (JwtUtil.isBlacklisted(token)) {
            log.warn("请求[{} {}]使用已失效 token: {}",
                    request.getMethod(), request.getRequestURI(), token);
            sendUnauthorizedResponse(response, TOKEN_INVALID);
            return false;
        }

        // 6. 验证token有效性并解析用户信息
        try {
            String userId = JwtUtil.getUserId(token);
            request.setAttribute("userId", userId);
            log.debug("请求[{} {}]验证通过，用户 ID: {}",
                    request.getMethod(), request.getRequestURI(), userId);
            return true;
        } catch (ExpiredJwtException e) {
            log.warn("请求[{} {}]使用过期 token: {}",
                    request.getMethod(), request.getRequestURI(), token);
            sendUnauthorizedResponse(response, TOKEN_EXPIRE);
        } catch (SignatureException e) {
            log.warn("请求[{} {}]使用无效签名 token: {}",
                    request.getMethod(), request.getRequestURI(), token);
            sendUnauthorizedResponse(response, TOKEN_SIGNATURE_INVALID);
        } catch (Exception e) {
            log.error("请求[{} {}]token 验证失败: {}",
                    request.getMethod(), request.getRequestURI(), e.getMessage());
            sendUnauthorizedResponse(response, TOKEN_INVALID);
        }
        return false;
    }

    /**
     * 判断是否为静态资源的GET请求
     */
    private boolean isStaticResourceGetRequest(HttpServletRequest request) {
        if (!"GET".equalsIgnoreCase(request.getMethod())) {
            return false;
        }

        String requestURI = request.getRequestURI();
        String fileExtensionPattern = ".*\\.(png|jpg|jpeg|gif|webp|txt|pdf|doc|docx|xls|xlsx|ico)$";
        boolean isStaticResource = requestURI.matches(fileExtensionPattern);

        if (isStaticResource) {
            log.info("GET请求访问静态资源，直接放行: {}", requestURI);
        } else {
            log.debug("GET请求但不是静态资源: {}", requestURI);
        }

        return isStaticResource;
    }

    /**
     * 从请求中提取token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");

        // 处理Bearer格式
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7).trim();
            log.debug("请求[{} {}]从 Authorization 头（Bearer 格式）获取 token: {}",
                    request.getMethod(), request.getRequestURI(), token);
            return token;
        }

        // 处理自定义token头
        String token = request.getHeader("token");
        if (token != null) {
            log.debug("请求[{} {}]从自定义 token 头获取 token: {}",
                    request.getMethod(), request.getRequestURI(), token);
        } else {
            log.debug("请求[{} {}]未找到 Authorization 或 token 头",
                    request.getMethod(), request.getRequestURI());
        }

        return token;
    }

    /**
     * 判断token是否为空
     */
    private boolean isTokenEmpty(String token) {
        return token == null || token.isEmpty();
    }

    /**
     * 发送未授权响应（统一处理 IO 异常）
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, ResultEnum resultEnum) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        ObjectMapper objectMapper = new ObjectMapper();
        Result<Object> result = Result.error(resultEnum);
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
