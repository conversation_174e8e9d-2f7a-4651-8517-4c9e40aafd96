package com.cjx.ollama.component;

import com.cjx.ollama.pojo.entity.Link;
import com.cjx.ollama.repository.LinkRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Author: cjx
 * Date: 2025/7/24
 * 延迟加载关键词-链接对的缓存，使用 AtomicReference 实现线程安全
 */
@Component
@RequiredArgsConstructor
public class LinkLoader {

    private final LinkRepository linkRepository;

    // 使用 AtomicReference 存储缓存，线程安全，支持懒加载与刷新
    private final AtomicReference<List<Entry<String, String>>> cachedEntryListRef = new AtomicReference<>();

    /**
     * 获取当前缓存的关键词-链接对列表（懒加载）
     * 第一次调用时自动从数据库加载，后续直接读取缓存
     */
    public List<Entry<String, String>> getCachedLinkEntries() {
        List<Entry<String, String>> cache = cachedEntryListRef.get();
        if (cache == null) {
            synchronized (this) {
                cache = cachedEntryListRef.get();
                if (cache == null) {
                    cache = loadFromDatabase();
                    cachedEntryListRef.set(cache);
                }
            }
        }
        return cache;
    }

    /**
     * 手动刷新缓存（例如用于定时任务或管理后台按钮）
     */
    public void refreshCache() {
        List<Entry<String, String>> cache = loadFromDatabase();
        cachedEntryListRef.set(cache);
    }

    /**
     * 从数据库加载关键词-链接对列表，并按关键词长度倒序排序（长关键词优先匹配）
     */
    private List<Entry<String, String>> loadFromDatabase() {
        List<Link> linkList = linkRepository.getLinkList();
        return linkList.stream()
                .map(link -> Map.entry(link.getKeyWord(), link.getUrl()))
                .sorted((a, b) -> Integer.compare(b.getKey().length(), a.getKey().length()))
                .toList(); // Java 16+，返回不可变只读集合
    }
}
