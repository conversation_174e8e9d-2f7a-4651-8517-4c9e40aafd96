package com.cjx.ollama.component;

import com.cjx.ollama.pojo.entity.Image;
import com.cjx.ollama.repository.ImageRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Author: cjx
 * Date: 2025/7/28
 * 图片链接懒加载缓存，按分类分组，线程安全
 */
@Component
@RequiredArgsConstructor
public class ImageLoader {

    private final ImageRepository imageRepository;

    // 使用原子引用存储缓存映射，支持线程安全的懒加载和刷新
    private final AtomicReference<Map<String, List<Image>>> cachedImageMapRef = new AtomicReference<>();

    /**
     * 获取指定分类的图片列表（懒加载）
     * 第一次访问时从数据库加载，后续读取缓存
     */
    public List<Image> getImagesByCategory(String category) {
        Map<String, List<Image>> cache = cachedImageMapRef.get();
        if (cache == null) {
            synchronized (this) {
                cache = cachedImageMapRef.get();
                if (cache == null) {
                    cache = loadFromDatabase();
                    cachedImageMapRef.set(cache);
                }
            }
        }
        return cache.getOrDefault(category, List.of());
    }

    /**
     * 手动刷新图片缓存（如后台管理操作或定时刷新任务）
     */
    public void refreshCache() {
        Map<String, List<Image>> newCache = loadFromDatabase();
        cachedImageMapRef.set(newCache);
    }

    /**
     * 从数据库中加载图片数据并按分类分组
     */
    private Map<String, List<Image>> loadFromDatabase() {
        List<Image> imageList = imageRepository.getImageList();
        return imageList.stream()
                .collect(Collectors.groupingBy(Image::getCategory));
    }
}
