package com.cjx.ollama.result;

import lombok.Data;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/29
 */
@Data
public class PageResult<T> {
    // 数据列表
    private List<T> list;
    // 总条数
    private long total;
    // 当前页码
    private int pageNum;
    // 每页条数
    private int pageSize;
    // 总页数
    private int totalPages;

    public PageResult(List<T> list, long total, int pageNum, int pageSize) {
        this.list = list;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.totalPages = (int) Math.ceil((double) total / pageSize);
    }
}
