package com.cjx.ollama.result;

import lombok.Getter;

/**
 * Author: cjx
 * Date: 2025/7/8
 * 结果枚举类
 */

@Getter
public enum ResultEnum {
    //JWT
    TOKEN_NULL(10000,"token为空"),
    TOKEN_INVALID(10000, "token无效"),
    TOKEN_EXPIRE(10003,"token过期"),
    TOKEN_SIGNATURE_INVALID(10001, "token签名无效"),

    // 通用状态码
    REQUEST_SUCCESS(20000, "请求成功!"),
    TOKEN_EMPTY(20001, "Token不能为空!"),

    // 用户状态码
    USERNAME_EXIT(30001, "用户名已存在!"),
    USERNAME_NOT_EXIST(30002, "用户名不存在,请前去注册!"),
    PASSWORD_ERROR(30003, "密码错误!"),
    USERNAME_EMPTY(30004, "用户名不能为空!"),
    PASSWORD_EMPTY(30005, "密码不能为空!"),
    USER_REGISTER_FAILED(30006, "用户注册失败!"),
    USER_LOGIN_FAILED(30007, "用户登录失败!"),
    USER_NOT_FOUND(30008,"用户信息未找到"),

    // 流式输出
    STREAM_TERMINAL(40001, "流已终止"),
    STREAM_NOT_FOUND(40002, "流未找到"),
    STREAM_STARTED(40003, "流已开始"),
    STREAM_COMPLETED(40004, "流已完成"),

    // 聊天相关
    MESSAGE_EMPTY(50001, "消息内容不能为空!"),
    SESSION_NOT_FOUND(50002, "会话不存在!"),
    SESSION_CREATED(50003, "会话创建成功!"),
    CHAT_SUCCESS(50004, "聊天成功!"),
    USER_ID_EMPTY(50005, "用户ID不能为空!"),
    STREAM_ID_EMPTY(50006, "流ID不能为空!"),
    // 消息相关
    SESSION_ID_EMPTY(50007, "会话ID不能为空!"),
    MESSAGE_NOT_FOUND(50008, "消息不存在!"),
    MESSAGE_QUERY_SUCCESS(50009, "消息查询成功!"),
    MESSAGE_LIST_EMPTY(50010, "消息列表为空!"),
    // 会话相关
    SESSION_CREATE_FAILED(50011, "会话创建失败!"),
    SESSION_DELETE_SUCCESS(50012, "会话删除成功!"),
    SESSION_DELETE_FAILED(50013, "会话删除失败!"),
    SESSION_QUERY_SUCCESS(50014, "会话查询成功!"),
    SESSION_LIST_EMPTY(50015, "会话列表为空!"),
    SESSION_EXPORT_SUCCESS(50016, "会话导出成功!"),
    SESSION_EXPORT_FAILED(50017, "会话导出失败!"),
    SESSION_NAME_EMPTY(50018, "会话名称不能为空!"),
    SESSION_ALREADY_EXPORTED(50019,"会话已导出"),
    IMAGE_CATEGORY_EMPTY(50020, "图片分类不能为空!"),
    POST_CATEGORY_EMPTY(50021, "文章分类不能为空!"),

    // 文件处理错误
    IMAGE_TYPE_ERROR(60001, "上传图片的格式不合法"),
    FILE_WRITE_ERROR(60002, "文件写入失败!"),
    FILE_READ_ERROR(60003, "文件读取失败!"),
    FILE_NOT_FOUND(60004, "文件不存在!"),
    FILE_UPLOAD_SUCCESS(60005, "文件上传成功!"),
    FILE_UPLOAD_FAILED(60006, "文件上传失败!"),
    FILE_EMPTY(60007, "文件为空!"),
    FILE_TYPE_NOT_SUPPORTED(60008, "文件类型不支持!"),
    DIRECTORY_CREATE_FAILED(60009, "目录创建失败!"),
    IMAGE_MAX_UPLOAD_NUM(60010, "图片上传数量达到上限!"),

    // HTML美化相关
    HTML_BEAUTIFY_SUCCESS(70001, "HTML美化成功!"),
    HTML_BEAUTIFY_FAILED(70002, "HTML美化失败!"),
    HTML_FILE_NOT_FOUND(70003, "HTML文件不存在!"),
    HTML_ALREADY_BEAUTIFIED(70004, "HTML文件已经美化过!"),
    NO_HTML_NEED_BEAUTIFY(70005, "没有HTML需要美化!"),

    // 图像生成相关
    IMAGE_GENERATE_SUCCESS(80001, "图像生成成功!"),
    IMAGE_GENERATE_FAILED(80002, "图像生成失败!"),
    IMAGE_DOWNLOAD_FAILED(80003, "图像下载失败!"),

    // 多模态分析相关
    MULTIMODAL_ANALYSIS_SUCCESS(90001, "多模态分析成功!"),
    MULTIMODAL_ANALYSIS_FAILED(90002, "多模态分析失败!"),
    FILENAME_EMPTY(90003, "文件名不能为空!"),
    FILE_CONTENT_EMPTY(90004, "文件内容为空!"),

    // 文生图
    TEXT_TO_IMAGE_FAILED(100001,"文生图失败"),
    QUOTA_INSUFFICIENT(100002,"额度不足"),
    TEXT_TO_IMAGE_MODEL_EXCEPTION(100003,"文生图模型调用异常"), UNSUPPORTED_FILE_TYPE(100003,"不支持的文件类型" ),

    //链接
    LINK_ID_EMPTY(110001,"链接id为空"),
    LINK_NOT_FOUND(110002,"链接id不存在"),
    LINK_NOT_MODIFIED(110003,"数据未变更"),
    LINK_INSERT_FAILED(110004,"链接插入失败"),
    LINK_DELETE_FAILED(110005,"链接删除失败"),
    LINK_UPDATE_FAILED(110006,"链接更新失败，未找到对应ID或数据未变更"),
    LINK_IMPORT_FAILED(110007,"EXCEL链接导入失败"),
    LINK_BATCH_DELETE_FAILED(110008,"批量删除链接失败"),

    //图片
    IMAGE_ID_EMPTY(120001,"链接id为空"),
    IMAGE_NOT_FOUND(120002,"链接id不存在"),
    IMAGE_NOT_MODIFIED(120003,"数据未变更"),
    IMAGE_INSERT_FAILED(120004,"链接插入失败"),
    IMAGE_DELETE_FAILED(120005,"链接删除失败"),
    IMAGE_UPDATE_FAILED(120006,"链接更新失败，未找到对应ID或数据未变更"),
    IMAGE_IMPORT_FAILED(120007,"EXCEL链接导入失败"),
    IMAGE_BATCH_DELETE_FAILED(120008,"批量删除链接失败");

    private final Integer code;
    private final String message;

    ResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
