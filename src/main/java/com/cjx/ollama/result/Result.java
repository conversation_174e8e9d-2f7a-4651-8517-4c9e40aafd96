package com.cjx.ollama.result;

import lombok.Data;

/**
 * Author: cjx
 * Date: 2025/7/8
 * 统一响应结果类
 */
@Data
public class Result<T> {
    private Integer code;       //响应码
    private String message;     //响应消息
    private T data;

    public Result() {}

    public Result(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Result(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultEnum.REQUEST_SUCCESS.getCode(), ResultEnum.REQUEST_SUCCESS.getMessage());
    }

    /**
     * 成功响应带数据
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultEnum.REQUEST_SUCCESS.getCode(), ResultEnum.REQUEST_SUCCESS.getMessage(), data);
    }

    /**
     * 失败响应
     */
    public static <T> Result<T> error(ResultEnum resultEnum) {
        return new Result<>(resultEnum.getCode(), resultEnum.getMessage());
    }

    /**
     * 失败响应带自定义消息
     */
    public static <T> Result<T> error(ResultEnum resultEnum, String message) {
        return new Result<>(resultEnum.getCode(), message);
    }
}
