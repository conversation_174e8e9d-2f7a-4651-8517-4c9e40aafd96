package com.cjx.ollama.utils.jwt;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import java.security.Key;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;

import static com.cjx.ollama.utils.constant.JWT.BLACKLIST_EXPIRATION;
import static com.cjx.ollama.utils.constant.JWT.EXPIRATION;

/**
 * Author: cjx
 * Date: 2025/7/4
 * JWT工具类，用于生成和解析JSON Web Token
 * 采用HS256算法进行签名，密钥自动生成并静态存储
 */
public class JwtUtil {
    private JwtUtil(){
        throw new UnsupportedOperationException("JwtUtil工具类不应该被实例化");
    }
    // 静态密钥，使用HS256算法自动生成安全密钥
    //Keys.secretKeyFor(...) 会每次启动服务时生成一个全新的随机密钥
    //未持久化
    //重启服务token会无效
    private static final Key key = Keys.secretKeyFor(SignatureAlgorithm.HS256);

    // JWT黑名单，存储已失效的token及其过期时间
    // ConcurrentHashMap
    // 在高并发场景下，既能保证线程安全，又能提供高效的读写性能，同时支持原子性操作和动态清理
    private static final ConcurrentHashMap<String, Long> blacklist = new ConcurrentHashMap<>();

    /**
     * 生成JWT令牌
     * @param userId 用户ID，作为JWT的subject
     * @return JWT字符串
     */
    public static String tokenGenerate(
            String userId
    ){
        return Jwts.builder()
                .setSubject(userId) //设置主题为用户ID
                .setIssuedAt(new Date()) // 设置签发时间为当前时间
                .setExpiration(new Date(System.currentTimeMillis()+EXPIRATION)) // 设置过期时间
                .signWith(key) // 使用HS256算法和密钥签名
                .compact(); // 生成JWT字符串
    }

    /**
     * 从JWT中解析用户ID
     * @param token JWT字符串
     * @return 用户ID
     */
    public static String getUserId(String token){
        return Jwts.parserBuilder()// 设置签名密钥
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)// 解析JWT
                .getBody()
                .getSubject();// 获取主题（用户ID）
    }

    /**
     * 将token添加到黑名单
     * @param token JWT token
     */
    public static void addToBlacklist(String token) {
        try {
            // 解析token获取过期时间
            Date expiration = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody()
                    .getExpiration();

            // 将token加入黑名单，过期时间比原token稍长
            long blacklistExpiration = Math.max(
                    expiration.getTime() + BLACKLIST_EXPIRATION,
                    System.currentTimeMillis() + BLACKLIST_EXPIRATION
            );

            blacklist.put(token, blacklistExpiration);

            // 启动清理任务，定期清理过期的黑名单记录
            cleanupExpiredBlacklist();

        } catch (Exception e) {
            // token解析失败，直接加入黑名单
            blacklist.put(token, System.currentTimeMillis() + BLACKLIST_EXPIRATION);
        }
    }

    /**
     * 检查token是否在黑名单中
     * @param token JWT token
     * @return true表示在黑名单中，false表示不在黑名单中
     */
    public static boolean isBlacklisted(String token) {
        Long expiration = blacklist.get(token);
        if (expiration == null) {
            return false;
        }

        // 检查是否已过期
        if (System.currentTimeMillis() > expiration) {
            blacklist.remove(token);
            return false;
        }

        return true;
    }

    /**
     * 清理过期的黑名单记录
     */
    private static void cleanupExpiredBlacklist() {
        long currentTime = System.currentTimeMillis();
        blacklist.entrySet().removeIf(entry -> entry.getValue() < currentTime);
    }
}
