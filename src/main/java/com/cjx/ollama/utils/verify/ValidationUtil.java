package com.cjx.ollama.utils.verify;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import org.springframework.web.multipart.MultipartFile;

/**
 * 参数校验工具类
 */
public class ValidationUtil {

    private ValidationUtil(){
        throw new UnsupportedOperationException("ValidationUtil工具类不应该被实例化");
    }

    /**
     * 校验用户ID
     * @param userId 用户ID
     * @throws CustomerException 当用户ID为空时抛出异常
     */
    public static void validateUserId(Long userId) {
        if (userId == null) {
            throw new CustomerException(ResultEnum.USER_ID_EMPTY);
        }
    }

    /**
     * 校验用户名
     * @param username 用户名
     * @throws CustomerException 当用户名为空时抛出异常
     */
    public static void validateUsername(String username) {
        if (StringUtils.isBlank(username)) {
            throw new CustomerException(ResultEnum.USERNAME_EMPTY);
        }
    }

    /**
     * 校验密码
     * @param password 密码
     * @throws CustomerException 当密码为空时抛出异常
     */
    public static void validatePassword(String password) {
        if (StringUtils.isBlank(password)) {
            throw new CustomerException(ResultEnum.PASSWORD_EMPTY);
        }
    }

    /**
     * 校验流ID
     * @param streamId 流ID
     * @throws CustomerException 当流ID为空时抛出异常
     */
    public static void validateStreamId(String streamId) {
        if (StringUtils.isBlank(streamId)) {
            throw new CustomerException(ResultEnum.STREAM_ID_EMPTY);
        }
    }

    /**
     * 校验会话ID
     * @param sessionId 会话ID
     * @throws CustomerException 当会话ID为空时抛出异常
     */
    public static void validateSessionId(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            throw new CustomerException(ResultEnum.SESSION_ID_EMPTY);
        }
    }

    /**
     * 校验消息内容
     * @param message 消息内容
     * @throws CustomerException 当消息为空时抛出异常
     */
    public static void validateMessage(String message) {
        if (StringUtils.isBlank(message)) {
            throw new CustomerException(ResultEnum.MESSAGE_EMPTY);
        }
    }

    /**
     * 校验文件名
     * @param filename 文件名
     * @throws CustomerException 当文件名为空时抛出异常
     */
    public static void validateFilename(String filename) {
        if (StringUtils.isBlank(filename)) {
            throw new CustomerException(ResultEnum.FILENAME_EMPTY);
        }
    }

    /**
     * 校验文件是否为空
     * @param file 文件
     * @throws CustomerException 当文件为空时抛出异常
     */
    public static void validateFileNotEmpty(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new CustomerException(ResultEnum.FILE_EMPTY);
        }
    }

    /**
     * 校验图片文件类型
     * @param file 文件
     * @throws CustomerException 当文件类型不支持时抛出异常
     */
    public static void validateImageFile(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new CustomerException(ResultEnum.FILE_TYPE_NOT_SUPPORTED);
        }
    }

    /**
     * 校验文本文件类型
     * @param file 文件
     * @throws CustomerException 当文件类型不支持时抛出异常
     */
    public static void validateTextFile(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".txt")) {
            throw new CustomerException(ResultEnum.FILE_TYPE_NOT_SUPPORTED);
        }
    }

    /**
     * 校验链接ID
     */
    public static void validateLinkId(Long linkId) {
        if (linkId == null) {
            throw new CustomerException(ResultEnum.LINK_ID_EMPTY);
        }
    }

    /**
     * 校验图片ID
     */
    public static void validateImageId(Long imageId) {
        if (imageId == null) {
            throw new CustomerException(ResultEnum.IMAGE_ID_EMPTY);
        }
    }

    /**
     * 校验文章分类
     */
    public static void validatePostCategory(String postCategory) {
        if (postCategory == null) {
            throw new CustomerException(ResultEnum.IMAGE_CATEGORY_EMPTY);
        }
    }

    /**
     * 校验图片分类
     */
    public static void validateImageCategory(String imageCategory) {
        if (imageCategory == null) {
            throw new CustomerException(ResultEnum.IMAGE_CATEGORY_EMPTY);
        }
    }
}