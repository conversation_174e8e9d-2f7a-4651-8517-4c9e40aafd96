package com.cjx.ollama.utils.constant;

/**
 * Author: cjx
 * Date: 2025/7/7
 */
public class Upload {
    private Upload() {
        // 防止通过反射创建实例
        throw new UnsupportedOperationException("Upload工具类不应被实例化");
    }
    // 图片上传目录（相对路径，相对于项目根目录）
    public static final String IMAGE_UPLOAD_DIR = "upload/image/";

    // 文本文件上传目录
    public static final String TEXT_UPLOAD_DIR = "upload/text/";

    // 批量图片上传目录
    public static final String BATCH_IMAGE_UPLOAD_DIR = "image/";


}
