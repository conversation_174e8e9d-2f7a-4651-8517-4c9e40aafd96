package com.cjx.ollama.utils.constant;

/**
 * Author: cjx
 * Date: 2025/7/3
 * 样式工具类
 */
public class CSS {
    private CSS() {
        // 防止通过反射创建实例
        throw new UnsupportedOperationException("CSS样式工具类不应被实例化");
    }
    public static final String BEAUTIFUL_CSS_1 = """
<!-- beautified-by-ollama -->
<style type="text/css">
  :root {
    --primary-color: #2c5282;
    --secondary-color: #4299e1;
    --accent-color: #3182ce;
    --light-color: #edf2f7;
    --dark-color: #1a202c;
    --text-color: #4a5568;
    --border-color: #e2e8f0;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #e53e3e;
  }
  
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  body {
    font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #ffffff;
    min-height: 100vh; /* 占满视口高度 */
    padding: 0;
  }
  
  .page-container {
    max-width: 100%; /* 确保容器占满屏幕宽度 */
    padding: 20px;
  }
  
  .title {
    font-size: clamp(1.8rem, 4vw, 2.8rem); /* 自适应字体大小 */
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin: 30px 0 25px;
    padding-bottom: 15px;
    border-bottom: 3px solid var(--accent-color);
  }
  
  .msg {
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: clamp(1.5rem, 3vw, 2rem); /* 自适应内边距 */
    width: 100%; /* 占满父容器宽度 */
  }
  
  p {
    margin-bottom: 1.5rem;
    font-size: clamp(1rem, 2vw, 1.1rem); /* 自适应字体大小 */
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: var(--dark-color);
    margin: clamp(1.5rem, 3vw, 2rem) 0 1rem;
    line-height: 1.3;
  }
  
  h3 {
    font-size: clamp(1.2rem, 3vw, 1.6rem); /* 自适应标题大小 */
    padding-left: 10px;
    border-left: 4px solid var(--secondary-color);
  }
  
  ul, ol {
    margin-left: 2rem;
    margin-bottom: 1.5rem;
  }
  
  li {
    margin-bottom: 0.7rem;
    position: relative;
  }
  
  strong {
    color: var(--dark-color);
    font-weight: 600;
  }
  
  .highlight {
    background-color: var(--light-color);
    padding: 2px 5px;
    border-radius: 4px;
  }
  
  .card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
  }
  
  .timeline {
    position: relative;
    margin-left: 20px;
  }
  
  .timeline::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    height: 100%;
    width: 2px;
    background-color: var(--border-color);
  }
  
  .timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 20px;
  }
  
  .timeline-item::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--secondary-color);
  }
  
  .badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
    margin-right: 5px;
  }
  
  .badge-primary {
    background-color: var(--primary-color);
    color: white;
  }
  
  .badge-secondary {
    background-color: var(--secondary-color);
    color: white;
  }
  
  .badge-success {
    background-color: var(--success-color);
    color: white;
  }
  
  .section-divider {
    height: 20px;
    background: linear-gradient(to right, transparent, var(--border-color), transparent);
    margin: 25px 0;
  }
  
  /* 全屏响应式优化 */
  @media (max-width: 768px) {
    .page-container {
      padding: 15px;
    }
    
    .msg {
      padding: 1.2rem;
    }
    
    ul, ol {
      margin-left: 1.5rem;
    }
  }
</style>
""";
}
