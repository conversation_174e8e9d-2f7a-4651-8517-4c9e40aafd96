package com.cjx.ollama.utils.constant;

/**
 * Author: cjx
 * Date: 2025/7/31
 */
public class Page {
    private Page() {
        throw new UnsupportedOperationException("Page工具类不应该被实例化");
    }
    /**
     * 默认页码，分页查询时若未指定页码则使用此值
     */
    public static final Integer DEFAULT_PAGE_NUM = 1;

    /**
     * 默认每页记录数，分页查询时若未指定每页条数则使用此值
     */
    public static final Integer DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大每页记录数限制，用于防止分页查询时因每页条数过大导致的性能问题
     */
    public static final Integer MAX_PAGE_SIZE = 100;
}
