package com.cjx.ollama.utils.constant;

/**
 * Author: cjx
 * Date: 2025/7/31
 */
public class Export {
    private Export(){
        throw new UnsupportedOperationException("Export工具类不应该被实例化");
    }

    //默认文章分类
    public static final String DEFAULT_POST_CATEGORY = "未分类"; // 默认文章分类
    //默认图片分类
    public static final String DEFAULT_IMAGE_CATEGORY = "未分类"; // 默认图片分类

    //会话标题txt保存位置
    public static final String SESSION_NAME_TXT_LOCATION = "export/question";
    //会话html保存位置
    public static final String SESSION_HTML_LOCATION = "export/session/html";
    //会话xml保存位置
    public static final String SESSION_XML_LOCATION = "export/session/xml";
    //会话excel保存位置
    public static final String SESSION_EXCEL_LOCATION = "export/session/excel";
    //会话csv保存位置
    public static final String SESSION_CSV_LOCATION = "export/session/csv";
}
