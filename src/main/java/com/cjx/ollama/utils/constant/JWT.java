package com.cjx.ollama.utils.constant;

/**
 * Author: cjx
 * Date: 2025/7/4
 */
public class JWT {
    private JWT(){
        throw new UnsupportedOperationException("JWT不应该被实例化");
    }
//    整数溢出风险
//    public static final long EXPIRATION = 1000 * 60 * 60 * 24; // 1天


    //将任意一个操作数强制转换为 long 类型
    //整个计算会自动提升为 long
    //避免溢出
    public static final long EXPIRATION = 1000L * 60 * 60 ; // 1h
    
    // JWT黑名单过期时间（比token过期时间长一些，确保覆盖）
    public static final long BLACKLIST_EXPIRATION = 1000L * 60 * 60 * 2; // 2h
}
