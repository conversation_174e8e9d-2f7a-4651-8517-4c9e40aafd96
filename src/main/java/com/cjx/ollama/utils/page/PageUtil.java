package com.cjx.ollama.utils.page;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cjx.ollama.pojo.info.PageInfo;
import com.cjx.ollama.pojo.dto.BaseQueryDto;
import lombok.extern.slf4j.Slf4j;

import static com.cjx.ollama.utils.constant.Page.*;

/**
 * 分页处理工具类
 */
@Slf4j
public class PageUtil {
    private PageUtil(){
        throw new UnsupportedOperationException("PageUtil工具类不应该被实例化");
    }

    /**
     * 为查询条件添加分页参数（基于BaseQueryDto）
     * @param queryWrapper MyBatis-Plus查询包装器
     * @param baseQueryDto 包含分页参数的DTO
     * @param <T> 实体类类型
     * @return 分页信息对象
     */
    public static <T> PageInfo addPagination(LambdaQueryWrapper<T> queryWrapper, BaseQueryDto baseQueryDto) {
        if (baseQueryDto == null) {
            log.debug("【addPagination】BaseQueryDto为null，使用默认分页参数");
            return addPaginationWithReturn(queryWrapper, null, null);
        }
        return addPaginationWithReturn(queryWrapper, baseQueryDto.getPageNum(), baseQueryDto.getPageSize());
    }

    /**
     * 为查询条件添加分页参数并返回分页信息
     * @param queryWrapper MyBatis-Plus查询包装器
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @param <T> 实体类类型
     * @return 分页信息对象
     */
    public static <T> PageInfo addPaginationWithReturn(LambdaQueryWrapper<T> queryWrapper, Integer pageNum, Integer pageSize) {
        // 处理页码，使用默认值如果为null或小于1
        int actualPageNum = (pageNum != null && pageNum >= 1) ? pageNum : DEFAULT_PAGE_NUM;

        // 处理每页条数，使用默认值如果为null或小于1，同时不超过最大限制
        int actualPageSize = (pageSize != null && pageSize >= 1) ? pageSize : DEFAULT_PAGE_SIZE;
        actualPageSize = Math.min(actualPageSize, MAX_PAGE_SIZE);

        // 应用分页参数并记录日志，直接指定当前方法名
        applyPagination(queryWrapper, actualPageNum, actualPageSize);

        return new PageInfo(actualPageNum, actualPageSize);
    }

    /**
     * 应用分页参数到查询包装器并记录日志
     * @param queryWrapper MyBatis-Plus查询包装器
     * @param pageNum 实际页码
     * @param pageSize 实际每页条数
     * @param <T> 实体类类型
     */
    private static <T> void applyPagination(LambdaQueryWrapper<T> queryWrapper, int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + offset);
        // 直接使用固定的方法名，因为当前只有addPaginationWithReturn调用此方法
        log.debug("【addPaginationWithReturn】应用分页条件: 页码={}, 每页条数={}, 偏移量={}",
                pageNum, pageSize, offset);
    }
}
