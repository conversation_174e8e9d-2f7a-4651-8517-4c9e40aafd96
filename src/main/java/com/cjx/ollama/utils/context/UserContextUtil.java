package com.cjx.ollama.utils.context;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文工具类
 */
@Slf4j
public class UserContextUtil {
    private UserContextUtil(){
        throw new UnsupportedOperationException("UserContextUtil工具类不应该被实例化");
    }
    /**
     * 从请求中获取用户ID
     * @param request HTTP请求
     * @return 用户ID
     * @throws CustomerException 当用户ID为空或格式错误时抛出异常
     */
    public static Long getUserIdFromRequest(HttpServletRequest request) {
        String userIdStr = (String) request.getAttribute("userId");
        if (StringUtils.isBlank(userIdStr)) {
            throw new CustomerException(ResultEnum.USER_ID_EMPTY);
        }
        try {
            return Long.valueOf(userIdStr);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误: {}", userIdStr);
            throw new CustomerException(ResultEnum.USER_ID_EMPTY);
        }
    }
} 