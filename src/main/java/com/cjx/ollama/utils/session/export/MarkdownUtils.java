package com.cjx.ollama.utils.session.export;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;

import java.util.List;

/**
 * Markdown 工具类
 * 整合Markdown转HTML基础功能和会话HTML生成功能
 * author cjx
 * date 2025/7/25
 */
public class MarkdownUtils {
    // 禁止实例化
    private MarkdownUtils() {
        throw new UnsupportedOperationException("MarkdownUtils工具类不应该被实例化");
    }

    // 基础解析器和渲染器（全局单例）
    private static final Parser PARSER = Parser.builder().build();
    private static final HtmlRenderer RENDERER = HtmlRenderer.builder().build();

    /**
     * 基础功能：Markdown文本转HTML文本
     * @param markdown Markdown格式的文本
     * @return 转换后的HTML文本（空输入返回空字符串）
     */
    public static String toHtml(String markdown) {
        if (markdown == null) {
            return "";
        }
        return RENDERER.render(PARSER.parse(markdown));
    }

    /**
     * 业务功能：生成会话完整HTML页面（包含样式）
     * @param session 会话信息
     * @param messages 消息列表
     * @return 完整的HTML页面字符串
     */
    public static String generateSessionHtml(ChatSession session, List<ChatMessage> messages) {
        String sessionName = session.getSessionName();
        StringBuilder html = new StringBuilder();

        // 构建HTML页面结构和样式
        html.append("<!DOCTYPE html><html lang='zh-CN'><head>")
                .append("<meta charset='UTF-8'><title>").append(sessionName).append("</title>")
                .append("<style>")
                .append("body{font-family:微软雅黑,Arial,sans-serif;margin:30px;line-height:1.6;}")
                .append(".title{font-size:24px;font-weight:bold;text-align:center;margin:20px 0 40px;}")
                .append(".msg{margin:20px 0;padding:15px;background:#f8f9fa;border-radius:4px;}")
                .append("h1{font-size:22px;margin:15px 0;}h2{font-size:20px;}h3{font-size:18px;}")
                .append("img{max-width:100%;height:auto;margin:10px 0;}")
                .append("</style></head><body>")
                .append("<div class='title'>").append(sessionName).append("</div>");

        // 渲染每条消息
        for (ChatMessage msg : messages) {
            String content = msg.getContent();
            if (StringUtils.isBlank(content)) {
                continue;
            }
            // 复用基础转换方法
            html.append("<div class='msg'>").append(toHtml(content)).append("</div>");
        }

        html.append("</body></html>");
        return html.toString();
    }

    /**
     * 业务功能：处理单条消息内容（Markdown转HTML）
     * @param msgContent 消息内容（Markdown格式）
     * @return 转换后的HTML内容
     */
    public static String processMessageContent(String msgContent) {
        // 复用基础转换方法
        return toHtml(msgContent);
    }
}
