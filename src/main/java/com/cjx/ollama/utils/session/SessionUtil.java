package com.cjx.ollama.utils.session;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.pojo.info.SessionInfo;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatMessageService;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;


/**
 * 会话验证工具类
 * 封装会话和消息的合法性校验逻辑
 */
public class SessionUtil {
    private SessionUtil() {
        throw new UnsupportedOperationException("SessionUtil工具类不应该被实例化");
    }

    /**
     * 验证会话归属并获取有效消息列表
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param sessionService 会话服务类（用于查询会话）
     * @param messageService 消息服务类（用于查询消息）
     * @return 封装好的会话信息（含会话实体和过滤后的消息）
     */
    public static SessionInfo getValidSessionAndMessages(
            Long userId,
            String sessionId,
            IService<ChatSession> sessionService,
            ChatMessageService messageService
    ) {
        // 1. 验证会话归属
        ChatSession session = sessionService.lambdaQuery()
                .eq(ChatSession::getUserId, userId)
                .eq(ChatSession::getSessionId, sessionId)
                .one();
        if (session == null) {
            throw new CustomerException(ResultEnum.SESSION_NOT_FOUND);
        }

        // 2. 获取消息列表（带权限校验）
        List<ChatMessage> messages = messageService.lambdaQuery()
                .eq(ChatMessage::getSessionId, sessionId)
                .apply("EXISTS (SELECT 1 FROM chat_session s WHERE s.session_id = chat_message.session_id AND s.user_id = {0})", userId)
                .orderByAsc(ChatMessage::getCreateTime)
                .list();

        if (messages.isEmpty()) {
            throw new CustomerException(ResultEnum.MESSAGE_LIST_EMPTY);
        }

        // 3. 过滤空内容和首条重复标题
        String sessionName = session.getSessionName();
        List<ChatMessage> filteredMessages = messages.stream()
                .filter(msg -> StringUtils.isNotBlank(msg.getContent()))
                .filter(msg -> !(messages.indexOf(msg) == 0 && msg.getContent().trim().equals(sessionName.trim())))
                .toList();

        if (filteredMessages.isEmpty()) {
            throw new CustomerException(ResultEnum.MESSAGE_LIST_EMPTY);
        }

        return new SessionInfo(session, filteredMessages);
    }
}