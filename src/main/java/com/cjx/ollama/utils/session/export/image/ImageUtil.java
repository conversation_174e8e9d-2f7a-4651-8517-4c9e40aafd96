package com.cjx.ollama.utils.session.export.image;

import com.cjx.ollama.pojo.entity.Image;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Random;

/**
 * Author: cjx
 * Date: 2025/7/28
 * 图片超链接工具类：在文本中合适的位置插入图片链接
 */
@Component
@RequiredArgsConstructor
public class ImageUtil {

    private final Random random = new Random();

    // 返回结果封装
    public record ImageInjectionResult(String htmlContent, String imageUrl) {}

    /**
     * 在 HTML 内容中插入一张图片（随机位置：头/中/尾）
     */
    public ImageInjectionResult insertImageLink(String htmlContent, List<Image> images) {
        if (images == null || images.isEmpty()) {
            return new ImageInjectionResult(htmlContent, "");
        }

        // 随机选择一张图片
        Image image = images.get(random.nextInt(images.size()));
        String imageUrl = image.getUrl();
        String description = image.getDescription();

        // 构建 <img> 标签
        String imgTag = buildImageTag(imageUrl, description);

        // 插入位置：头、中、尾
        String newHtml = insertAtRandomPosition(htmlContent, imgTag);

        return new ImageInjectionResult(newHtml, imageUrl);
    }

    /**
     * 构建图片 HTML 标签，带有增强的样式效果
     */
    private String buildImageTag(String url, String alt) {
        return String.format(
                "<p><img src=\"%s\" alt=\"%s\" style=\"" +
                        "max-width:100%%; " +
                        "height:auto; " +
                        "margin:15px 0; " +
                        "border-radius:8px; " +
                        "box-shadow:0 2px 8px rgba(0,0,0,0.15); " +
                        "border:1px solid #f0f0f0; " +
                        "transition:all 0.3s ease;\"" +
                        " onmouseover=\"this.style.transform='scale(1.01)';this.style.boxShadow='0 4px 12px rgba(0,0,0,0.2)'\"" +
                        " onmouseout=\"this.style.transform='scale(1)';this.style.boxShadow='0 2px 8px rgba(0,0,0,0.15)'\"" +
                        "/></p>",
                url, alt == null ? "" : alt
        );
    }

    /**
     * 将图片标签插入到 HTML 的随机位置（段落之间，而非字符中间）
     */
    private String insertAtRandomPosition(String html, String imgTag) {
        int type = random.nextInt(3);

        return switch (type) {
            case 0 -> // 头部
                    imgTag + html;
            case 1 -> // 中部：按段落中间插入，避免破坏 HTML 结构
                    insertInMiddleParagraph(html, imgTag); // 尾部
            default -> html + imgTag;
        };
    }

    /**
     * 在 HTML 中段落之间插入图片
     */
    private String insertInMiddleParagraph(String html, String imgTag) {
        String[] parts = html.split("</p>", -1); // 分段（保留空白段）
        if (parts.length <= 1) {
            return html + imgTag; // 如果没有段落，就插在尾部
        }

        int midIndex = parts.length / 2;
        StringBuilder builder = new StringBuilder();

        for (int i = 0; i < parts.length; i++) {
            builder.append(parts[i]);
            if (i != parts.length - 1) {
                builder.append("</p>");
            }
            if (i == midIndex) {
                builder.append(imgTag);
            }
        }

        return builder.toString();
    }

}
