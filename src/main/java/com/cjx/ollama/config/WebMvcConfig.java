package com.cjx.ollama.config;

import com.cjx.ollama.interceptor.JwtInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Author: cjx
 * Date: 2025/7/4
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final JwtInterceptor jwtInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
//                 需要校验token的路径  拦截所有接口
                .addPathPatterns("/**")
                .excludePathPatterns(
                        //登录注册不拦截
                        "/user/login",
                        "/user/register",
                        //显示放行静态资源文件
                        "/files/**"
                        // 注意：静态资源访问通过JWT拦截器内部的逻辑处理（基于文件扩展名）
                        // /upload/image等API接口仍需要JWT验证
                );
    }

}

