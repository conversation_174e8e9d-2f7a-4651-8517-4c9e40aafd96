package com.cjx.ollama.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import com.cjx.ollama.utils.context.UserContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.schema.Column;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;



/**
 * MyBatis-Plus 配置类：集成数据权限拦截器（适配从Request获取userId）
 * 自动为特定数据库表的查询添加用户 ID 过滤条件
 */
@Configuration
public class MyBatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 添加数据权限拦截器
        DataPermissionInterceptor dataPermissionInterceptor = new DataPermissionInterceptor();
        dataPermissionInterceptor.setDataPermissionHandler(new CustomDataPermissionHandler());
        interceptor.addInnerInterceptor(dataPermissionInterceptor);

        return interceptor;
    }

    /**
     * 自定义数据权限处理器：从当前请求中获取userId并自动添加过滤条件
     */
    static class CustomDataPermissionHandler implements DataPermissionHandler {
        @Override
        public Expression getSqlSegment(Expression where, String mappedStatementId) {
            // 1. 排除所有UserMapper的方法（登录/注册相关查询）
            if (
                    mappedStatementId.startsWith("com.cjx.ollama.mapper.UserMapper") ||
                    mappedStatementId.startsWith("com.cjx.ollama.mapper.LinkMapper") ||
                    mappedStatementId.startsWith("com.cjx.ollama.mapper.ImageMapper")
            ) {
                return where; // 不添加任何userId过滤条件
            }

            // 2. 非Web请求场景（如定时任务），不添加过滤
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return where;
            }
            HttpServletRequest request = attributes.getRequest();

            // 3. 从请求中获取userId（此时已排除登录场景，userId由JwtInterceptor提前设置）
            Long userId = UserContextUtil.getUserIdFromRequest(request);

            // 4. 对其他需要权限控制的表添加userId过滤
            if (
                    mappedStatementId.startsWith("com.cjx.ollama.mapper.ChatSessionMapper") ||
                    mappedStatementId.startsWith("com.cjx.ollama.mapper.TextImageMapper")
            ) {
                EqualsTo equalsTo = new EqualsTo();
                equalsTo.setLeftExpression(new Column("user_id"));
                equalsTo.setRightExpression(new LongValue(userId));
                return where == null ? equalsTo : new AndExpression(where, equalsTo);
            }

            return where;
        }
    }
}