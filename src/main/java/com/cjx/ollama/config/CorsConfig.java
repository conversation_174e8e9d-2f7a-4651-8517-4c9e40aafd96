package com.cjx.ollama.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry
                // 所有路径（任意目录）
                .addMapping("/**")
                // 用 allowedOriginPatterns 替代 allowedOrigins（避免通配符冲突）
                .allowedOriginPatterns("http://localhost:*") // 前端地址，根据实际前端域名调整
                // 所有请求方式（任意HTTP方法） GET POST PUT DELETE OPTIONS
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                // 所有请求头（如 Content-Type、自定义 Token 头）
                .allowedHeaders("*")
                .allowCredentials(true)
                // 预检请求缓存时间，单位秒，这里设置1小时，可根据实际需求调整
                .maxAge(3600L);
    }
}