package com.cjx.ollama.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Author: cjx
 * Date: 2025/7/28
 * 静态资源映射
 */
@Configuration
public class StaticResourceConfig implements WebMvcConfigurer {
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 主要的静态资源映射 - 直接映射到项目根目录下的文件夹
        registry
                .addResourceHandler("/upload/**")
                .addResourceLocations("file:./upload/")
                .setCachePeriod(3600);
        registry
                .addResourceHandler("/image/**")
                .addResourceLocations("file:./image/")
                .setCachePeriod(3600);
        registry
                .addResourceHandler("/static/**")
                .addResourceLocations("file:./static/");
        registry
                .addResourceHandler("/export/**")
                .addResourceLocations("file:./export/");
        registry
                .addResourceHandler("/text/**")
                .addResourceLocations("file:./text/");

        // 备用的静态资源映射 - 使用 /files/ 前缀
        registry
                .addResourceHandler("/files/upload/**")
                .addResourceLocations("file:./upload/");
        registry
                .addResourceHandler("/files/image/**")
                .addResourceLocations("file:./image/");
        registry
                .addResourceHandler("/files/static/**")
                .addResourceLocations("file:./static/");
        registry
                .addResourceHandler("/files/export/**")
                .addResourceLocations("file:./export/");
        registry
                .addResourceHandler("/files/text/**")
                .addResourceLocations("file:./text/");
    }
}
