package com.cjx.ollama.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Author: cjx
 * Date: 2025/7/14
 */
@Data
@TableName("multimodel")
public class Multimodal implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long multimodelId;
    private Long userId;
    private String filename;
    private Integer fileType; // 1-图像，2-文本
    private String filePath;
    private String content; // 文本内容
    private Integer status; // 1-分析中，2-分析完成，3-分析失败
    private String analysisResult; // 分析结果
    private LocalDateTime createTime;
}
