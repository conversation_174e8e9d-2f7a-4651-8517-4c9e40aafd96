package com.cjx.ollama.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Author: cjx
 * Date: 2025/7/1
 */
@TableName("user")
@Data
public class User implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long userId;
    private String userName;
    private String passWord;
    private String avatar;
    private LocalDateTime createTime;
}
