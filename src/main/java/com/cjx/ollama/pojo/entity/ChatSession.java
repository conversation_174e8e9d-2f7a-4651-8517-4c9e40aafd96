package com.cjx.ollama.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("chat_session")
@Accessors(chain = true)  //生成gs方法 支持链式调用
public class ChatSession implements Serializable {
    @Schema(description = "会话id")
    @TableId(type = IdType.INPUT)
    private String sessionId;
    private Long userId;
    @Schema(description = "会话名称")
    private String sessionName;
    @TableField("last_message")
    private String lastMessage;
    private LocalDateTime createTime;


    /*
     *      或者 有一个临时字段，用于返回最后一条消息内容（不映射到数据库）
     */

}