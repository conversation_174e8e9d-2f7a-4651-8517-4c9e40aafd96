package com.cjx.ollama.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Author: cjx
 * Date: 2025/7/25
 */
@Data
@TableName("image")
public class Image {
    @TableId(value = "image_id", type = IdType.AUTO)
    private Long imageId;
    private String url;
    private String category;
    private String description;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("update_time")
    private LocalDateTime updateTime;
}
