package com.cjx.ollama.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Author: cjx
 * Date: 2025/7/9
 * 文生图实体类
 */
@Data
@TableName("text_image")
public class TextImage implements Serializable {
    
    @TableId(type = IdType.AUTO)
    private Long imageId;
    
    @TableField("user_id")
    private Long userId;

    //图像质量
    private String quality;
    
    // 用户提示词
    private String message;
    
    // 图片url
    @TableField("image_url")
    private String imageUrl;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private LocalDateTime createTime;

}
