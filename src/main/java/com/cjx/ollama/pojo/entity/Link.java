package com.cjx.ollama.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Author: cjx
 * Date: 2025/7/24
 * 链接实体类
 */
@Data
@TableName("link")
public class Link {
    @TableId(value = "link_id", type = IdType.AUTO)
    private Long linkId;
    @TableField("key_word")
    private String keyWord;
    private String url;
    private String category;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("update_time")
    private LocalDateTime updateTime;
}
