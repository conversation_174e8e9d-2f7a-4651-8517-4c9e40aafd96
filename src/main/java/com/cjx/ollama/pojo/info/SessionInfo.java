package com.cjx.ollama.pojo.info;


import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;

import java.util.List;

/**
 * 会话信息封装类
 * 包含验证通过的会话实体和消息列表
 * record：
 *  “数据载体类”（仅用于存储数据的类）
 *  自动生成：
 *  （1）
 *  1.所有字段的private final声明（不可变）
 *  2.全参数构造方法
 *  3.每个字段的getter方法
 *  4.重写的equals()、hashCode()（基于所有字段）
 *  5.重写的toString()（包含所有字段的字符串表示）
 *  （2）
 *  1.record的所有字段默认是final，一旦创建对象就无法修改字段值（线程安全的基础）
 */
public record SessionInfo(
        ChatSession session,  // 会话实体
        List<ChatMessage> messages  // 过滤后的消息列表
) {}
