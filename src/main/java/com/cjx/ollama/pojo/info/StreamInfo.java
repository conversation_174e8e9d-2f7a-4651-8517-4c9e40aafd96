package com.cjx.ollama.pojo.info;

import lombok.Data;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 流信息封装类
 */
@Data
public class StreamInfo {

    /**
     * 全局并发安全 Map，用于存储 streamId 与对应的流状态信息
     * 封装为 private，避免外部直接访问
     */
    private static final ConcurrentMap<String, StreamInfo> streamStopMap = new ConcurrentHashMap<>();

    /**
     * 停止标志位
     */
    private final AtomicBoolean stopFlag;

    /**
     * 创建时间（时间戳）
     */
    private final long createTime;

    public StreamInfo() {
        this.stopFlag = new AtomicBoolean(false);
        this.createTime = System.currentTimeMillis();
    }

    /**
     * 获取所有流状态，仅建议内部使用（如定时清理任务）
     */
    public static ConcurrentMap<String, StreamInfo> getAll() {
        return streamStopMap;
    }

    // 获取
    public static StreamInfo getStreamInfo(String streamId) {
        return streamStopMap.get(streamId);
    }

    // 添加或覆盖
    public static void putStreamInfo(String streamId, StreamInfo info) {
        streamStopMap.put(streamId, info);
    }

    // 删除
    public static void removeStreamInfo(String streamId) {
        streamStopMap.remove(streamId);
    }

    // 判断是否存在
    public static boolean contains(String streamId) {
        return streamStopMap.containsKey(streamId);
    }
}
