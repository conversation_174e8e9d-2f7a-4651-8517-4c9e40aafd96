package com.cjx.ollama.service;

import com.cjx.ollama.pojo.entity.Multimodal;

import java.util.List;

/**
 * 多模态服务接口
 * 提供图像和文本的AI识别与分析功能
 * Author: cjx
 * Date: 2025/7/8
 */
public interface MultimodalService {
    

    
    /**
     * 分析上传的图像文件
     * @param filename 文件名
     * @return 识别结果
     */
    String imageAnalyze(Long userId,String filename);

    
    /**
     * 分析上传的文本文件
     * @param filename 文件名
     * @return 识别结果
     */
    String textAnalyze(Long userId,String filename);

    /**
     * 查询多模态历史记录
     */
    List<Multimodal> getMultimodelByUserId(Long userId);



}
