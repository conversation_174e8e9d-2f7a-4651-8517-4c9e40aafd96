package com.cjx.ollama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.dto.BaseQueryDto;
import com.cjx.ollama.pojo.entity.Link;
import com.cjx.ollama.result.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/24
 */
public interface LinkService extends IService<Link> {

    PageResult<Link> getLinkList(String keyWord, String category, BaseQueryDto baseQueryDto);

    Link insertLink(Link link);


    void deleteLink(Long linkId);

    Link updateLink(Link link);

    List<Link> importLinksByExcel(MultipartFile excel);

    void batchInsertLinks(List<Link> linkList);

    void batchDeleteLinks(List<Long> linkIds);
}
