package com.cjx.ollama.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.entity.TextImage;

import java.util.List;


/**
 * Author: cjx
 * Date: 2025/7/9 
 */
public interface TextImageService extends IService<TextImage> {

    /**
     * 文生图模型调用
     */
    String generateImage(Long userId, String message, String quality);


    /**
        获取当前用户图像生成列表
     */
    List<TextImage> getImageGenerateByUserId(Long userId);
}
