package com.cjx.ollama.service;

import com.cjx.ollama.result.Result;
import reactor.core.publisher.Flux;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Mono;

/**
 * 聊天服务接口
 */
public interface ChatService {

    /**
     * 查询当前模型
     * model: deepseek-r1:1.5b
     */
    Mono<Result<String>> getModel();

    /**
     * 普通对话
     * @param userId 用户ID
     * @param message 用户消息
     * @param sessionId 会话ID（可选）
     * @return AI回复内容
     */
    String chatCall(Long userId, String message, String sessionId);

    /**
     * 流式对话
     * @param userId 用户ID
     * @param message 用户消息
     * @param sessionId 会话ID（可选）
     * @param streamId 流ID
     * @return 流式响应
     */
    Flux<ServerSentEvent<String>> chatStream(Long userId, String message, String sessionId, String streamId);

    /**
     * 终止流式输出
     * @param streamId 流ID
     * @return 是否成功终止
     */
    boolean stopStream(String streamId);
} 