package com.cjx.ollama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.entity.ChatSession;

import java.util.List;

/**
 * 会话服务接口
 * Author: cjx
 * Date: 2025/6/30
 */
public interface ChatSessionService extends IService<ChatSession> {
    
    /**
     * 创建新会话
     * @param userId 用户ID
     * @param message 初始消息（用于生成会话名称）
     * @return 会话ID
     */
    String insertSession(Long userId, String message);
    
    /**
     * 删除会话及其相关消息
     * @param sessionId 会话ID
     * @return 是否删除成功
     */
    boolean deleteSession(String sessionId);
    
    /**
     * 获取用户的会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ChatSession> getChatSessionByUserId(Long userId);
    

}
