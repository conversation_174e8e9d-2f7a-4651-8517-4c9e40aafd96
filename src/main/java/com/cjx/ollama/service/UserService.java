package com.cjx.ollama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.dto.UserDto;
import com.cjx.ollama.pojo.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * Author: cjx
 * Date: 2025/7/4
 */
public interface UserService extends IService<User> {
    
    /**
     * 用户注册
     *   用户名
     *   密码
     * @return 注册结果
     */
    String register(UserDto userDto);
    
    /**
     * 用户登录
     *   用户名
     *   密码
     * @return JWT token
     */
    String login(UserDto userDto);

    /**
     * 用户退出
     * 使当前用户持有的 JWT Token 失效---加入jwt黑名单
     * 下次登录是颁发新token
     * @param userId 用户ID
     * @return 退出结果
     */
    String quit(Long userId);
    
    /**
     * 用户注销
     * 使当前用户持有的 JWT Token 失效，并清除用户数据
     * @param userId 用户ID
     * @return 注销结果
     */
    String logout(Long userId);

    /**
     * 获取用户信息接口
     */
    User getUserInfo(Long userId);

    /**
     * 用户列表
     */
    List<User> getUserList();

    /**
     * 更新用户
     */
    void updateUser(User user);

    /**
     * 删除用户
     */
    void deleteUser(Long userId);
}
