package com.cjx.ollama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.dto.BaseQueryDto;
import com.cjx.ollama.pojo.entity.Image;
import com.cjx.ollama.result.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/25
 */
public interface ImageService extends IService<Image> {
    PageResult<Image> getImageList(String category, String description, BaseQueryDto baseQueryDto);

    Image insertImage(Image image);

    void deleteImage(Long imageId);

    Image updateImage(Image image);

    void batchDeleteImages(List<Long> imageIds);

    List<Image> importImagesByExcel(MultipartFile excel);

    void batchInsertImages(List<Image> newImages);

    List<String> batchUploadImages(String category, String description, MultipartFile[] files);
}
