package com.cjx.ollama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.entity.ChatMessage;

import java.util.List;

/**
 * 消息服务接口
 * Author: cjx
 * Date: 2025/6/25
 */
public interface ChatMessageService extends IService<ChatMessage> {
    
    /**
     * 获取会话的所有消息
     * @param sessionId 会话ID
     * @return 消息列表
     */
    List<ChatMessage> getMessageList(String sessionId);
    
    /**
     * 获取会话的最新N条消息
     * @param sessionId 会话ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<ChatMessage> getLatestMessages(String sessionId, Integer limit);
    
    /**
     * 获取会话的消息数量
     * @param sessionId 会话ID
     * @return 消息数量
     */
    Long getMessageCount(String sessionId);
    
    /**
     * 删除会话的所有消息
     * @param sessionId 会话ID
     * @return 删除的消息数量
     */
    Long deleteMessages(String sessionId);
}
