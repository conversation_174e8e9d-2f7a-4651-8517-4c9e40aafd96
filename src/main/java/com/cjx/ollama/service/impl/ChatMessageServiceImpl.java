package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.mapper.ChatMessageMapper;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

/**
 * 消息服务实现类
 * Author: cjx
 * Date: 2025/6/25
 */
@Slf4j
@Service
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageMapper, ChatMessage> implements ChatMessageService {

    @Override
    public List<ChatMessage> getMessageList(String sessionId) {
        log.info("获取会话消息，sessionId: {}", sessionId);
        ValidationUtil.validateSessionId(sessionId);
        // 查询消息列表
        List<ChatMessage> messageList = this.lambdaQuery()
                .eq(ChatMessage::getSessionId, sessionId)
                .orderByAsc(ChatMessage::getCreateTime) // 按创建时间升序排列
                .list();
        if (messageList.isEmpty()) {
            log.info("会话 {} 的消息列表为空", sessionId);
            return messageList;
        }
        log.info("获取会话消息成功，sessionId: {}, 消息数量: {}", sessionId, messageList.size());
        return messageList;
    }

    @Override
    public List<ChatMessage> getLatestMessages(String sessionId, Integer limit) {
        log.info("获取最新消息，sessionId: {}, limit: {}", sessionId, limit);
        
        ValidationUtil.validateSessionId(sessionId);
        
        // 校验limit参数
        if (limit == null || limit <= 0 || limit > 100) {
            limit = 10; // 默认限制为10条，最大100条
            log.warn("limit参数超出范围，已重置为默认值: {}", limit);
        }
        
        // 查询最新消息
        List<ChatMessage> messages = this.lambdaQuery()
                .eq(ChatMessage::getSessionId, sessionId)
                .orderByDesc(ChatMessage::getCreateTime) // 按创建时间降序排列
                .last("LIMIT " + limit) // 限制返回数量
                .list();
        
        if (messages.isEmpty()) {
            log.info("会话 {} 的最新消息列表为空", sessionId);
            return messages; // 直接返回空列表
        }

        // 重新按时间升序排列，保持对话顺序
        messages.sort(Comparator.comparing(ChatMessage::getCreateTime));
        
        log.info("获取最新消息成功，sessionId: {}, 消息数量: {}", sessionId, messages.size());
        return messages;
    }

    @Override
    public Long getMessageCount(String sessionId) {
        log.info("获取消息数量，sessionId: {}", sessionId);
        
        ValidationUtil.validateSessionId(sessionId);
        
        // 统计消息数量
        Long count = this.lambdaQuery()
                .eq(ChatMessage::getSessionId, sessionId)
                .count();
        
        log.info("获取消息数量成功，sessionId: {}, 数量: {}", sessionId, count);
        return count;
    }

    @Override
    public Long deleteMessages(String sessionId) {
        log.info("删除会话消息，sessionId: {}", sessionId);
        
        ValidationUtil.validateSessionId(sessionId);
        
        // 先查询消息数量
        Long count = this.lambdaQuery()
                .eq(ChatMessage::getSessionId, sessionId)
                .count();
        
        if (count == 0) {
            log.warn("会话 {} 没有消息可删除", sessionId);
            throw new CustomerException(ResultEnum.MESSAGE_LIST_EMPTY);
        }
        
        // 删除消息
        boolean deleted = this.lambdaUpdate()
                .eq(ChatMessage::getSessionId, sessionId)
                .remove();
        
        if (deleted) {
            log.info("删除会话消息成功，sessionId: {}, 删除数量: {}", sessionId, count);
            return count;
        } else {
            log.error("删除会话消息失败，sessionId: {}", sessionId);
            throw new CustomerException(ResultEnum.REQUEST_SUCCESS);
        }
    }
}
