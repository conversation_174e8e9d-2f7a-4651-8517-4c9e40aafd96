package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.mapper.TextImageMapper;
import com.cjx.ollama.pojo.entity.TextImage;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.TextImageService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.image.ImagePrompt;
import org.springframework.ai.image.ImageResponse;
import org.springframework.ai.openai.OpenAiImageModel;
import org.springframework.ai.openai.OpenAiImageOptions;
import org.springframework.ai.openai.api.OpenAiImageApi;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TextImageServiceImpl extends ServiceImpl<TextImageMapper, TextImage> implements TextImageService {

    private final OpenAiImageModel openAiImageModel;
    private final TextImageMapper textImageMapper;

    @Override
    public String generateImage(Long userId, String message, String quality) {
        try{
        // 调用模型生成图像
        ImageResponse response = openAiImageModel.call(
                new ImagePrompt(
                        message,
                        OpenAiImageOptions.builder()
                                .model(OpenAiImageApi.DEFAULT_IMAGE_MODEL)  // dall-e-3
                                .quality(quality) // 图片质量 standard  hd
                                .N(1)
                                .height(1024)
                                .width(1024)
                                .build()
                )
        );

        String url = response.getResult().getOutput().getUrl();

        if(url==null || url.isEmpty()){
            throw new CustomerException(ResultEnum.TEXT_TO_IMAGE_FAILED);
        }

        // 保存到数据库
        saveTextImage(userId, message, quality,url);

        log.info("图片已成功生成 url:{}", url);
        return url;
    }catch (Exception e){
            // 检查是否是额度不足的403错误
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("HTTP 403") && errorMsg.contains("insufficient_user_quota")) {
                throw new CustomerException(ResultEnum.QUOTA_INSUFFICIENT);
            }else {
                log.error("调用OpenAI图像生成API失败，用户ID: {}, 描述: {}", userId, message,e);
                throw new CustomerException(ResultEnum.TEXT_TO_IMAGE_MODEL_EXCEPTION);
            }
        }
    }

    @Override
    public List<TextImage> getImageGenerateByUserId(Long userId) {
        log.info("获取用户文生图列表，userId: {}", userId);
        ValidationUtil.validateUserId(userId);
        //查询雅虎的文生图记录
        //按时间顺序排列
        List<TextImage> textImageList = this.lambdaQuery()
                .eq(TextImage::getUserId, userId)
                .orderByAsc(TextImage::getCreateTime)
                .list();
        //如果为空
        if(textImageList==null || textImageList.isEmpty()){
            log.info("用户 {} 的文生图列表为空", userId);
            return textImageList;
        }

        log.info("获取用户文生图列表成功，userId: {}, 文生图数量: {}", userId, textImageList.size());
        return textImageList;

    }


    // 保存图像信息到数据库
    private void saveTextImage(Long userId, String message, String quality, String url) {
        TextImage textImage = new TextImage();
        textImage.setUserId(userId);
        textImage.setMessage(message);
        textImage.setQuality(quality);
        textImage.setImageUrl(url);
        textImage.setCreateTime(LocalDateTime.now());

        // 使用MyBatis-Plus的基本方法
        textImageMapper.insert(textImage);
    }
}