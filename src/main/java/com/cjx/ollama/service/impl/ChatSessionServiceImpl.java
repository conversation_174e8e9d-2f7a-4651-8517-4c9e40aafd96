package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.mapper.ChatSessionMapper;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.service.ChatSessionService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


import static com.cjx.ollama.utils.constant.Parameter.*;

/**
 * Author: cjx
 * Date: 2025/6/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatSessionServiceImpl extends ServiceImpl<ChatSessionMapper, ChatSession> implements ChatSessionService {

    private final ChatMessageService chatMessageService;

    @Override
    public String insertSession(Long userId, String message) {
        log.info("创建新会话，userId: {}, message: {}", userId, message);
        
        ValidationUtil.validateUserId(userId);
        
        // 生成会话ID
        String sessionId = UUID.randomUUID().toString();
        
        // 处理会话名称
        // 如果 message 为空，使用默认的会话名称
        String sessionName = DEFAULT_SESSION_NAME;
        if (StringUtils.isNotBlank(message)) {
            if (message.length() > 15) {
                sessionName = message.substring(0, 15) + "...";
            } else {
                sessionName = message;
            }
        }
        
        // 创建会话对象
        ChatSession session = new ChatSession()
                .setSessionId(sessionId)
                .setUserId(userId)
                .setSessionName(sessionName)
                .setCreateTime(LocalDateTime.now());
        
        // 保存会话
        boolean saved = this.save(session);
        if (!saved) {
            throw new CustomerException(ResultEnum.SESSION_CREATE_FAILED);
        }
        
        log.info("新建会话成功，sessionId: {}, sessionName: {}", sessionId, sessionName);
        return sessionId;
    }

    @Override
    //此处通过数据库级联更新实现同步删除会话下的消息
    //也可删除会话时同步删除消息 注意保证事务一致性
    public boolean deleteSession(String sessionId) {
        log.info("删除会话及其消息，sessionId: {}", sessionId);
        ValidationUtil.validateSessionId(sessionId);
        // 检查会话是否存在
        ChatSession session = this.getById(sessionId);
        if (session == null) {
            throw new CustomerException(ResultEnum.SESSION_NOT_FOUND);
        }
        // 删除会话
        boolean sessionDeleted = this.removeById(sessionId);
        if (!sessionDeleted) {
            throw new CustomerException(ResultEnum.SESSION_DELETE_FAILED);
        }
        log.info("删除会话成功，sessionId: {}", sessionId);
        return true;
    }

    @Override
    public List<ChatSession> getChatSessionByUserId(Long userId) {
        log.info("获取用户会话列表，userId: {}", userId);
        
        ValidationUtil.validateUserId(userId);
        
        // 查询特定用户的会话，按创建时间倒序排列
        List<ChatSession> sessionList = this.lambdaQuery()
                .eq(ChatSession::getUserId, userId)
                .orderByDesc(ChatSession::getCreateTime)
                .list();
        
        if (sessionList.isEmpty()) {
            log.info("用户 {} 的会话列表为空", userId);
            return sessionList;
        }
        
        // 为每个会话补充最后一条消息内容
        for (ChatSession session : sessionList) {
            setLastMessageForSession(session);
        }
        
        log.info("获取用户会话列表成功，userId: {}, 会话数量: {}", userId, sessionList.size());
        return sessionList;
    }


    /**
     * 为会话设置最后一条消息内容，异常时设置为空
     */
    private void setLastMessageForSession(ChatSession session) {
        try {
            ChatMessage lastMsg = chatMessageService.lambdaQuery()
                    .eq(ChatMessage::getSessionId, session.getSessionId())
                    .orderByDesc(ChatMessage::getCreateTime)
                    .last("limit 1")
                    .one();
            session.setLastMessage(lastMsg != null ? lastMsg.getContent() : "");
        } catch (Exception e) {
            log.warn("获取会话 {} 的最后消息失败", session.getSessionId(), e);
            session.setLastMessage("");
        }
    }


}
