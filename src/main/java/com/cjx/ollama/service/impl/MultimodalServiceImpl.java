package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.mapper.MultimodalMapper;
import com.cjx.ollama.pojo.entity.Multimodal;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.MultimodalService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

import static com.cjx.ollama.utils.constant.Prompt.DEFAULT_SYSTEM_MESSAGE_PROMPT_MULTI_MODE_IMAGE_RECOGNITION;
import static com.cjx.ollama.utils.constant.Prompt.DEFAULT_SYSTEM_MESSAGE_PROMPT_MULTI_MODE_TEXT_RECOGNITION;
import static com.cjx.ollama.utils.constant.Upload.IMAGE_UPLOAD_DIR;
import static com.cjx.ollama.utils.constant.Upload.TEXT_UPLOAD_DIR;

/**
 * 多模态服务实现类
 * Author: cjx
 * Date: 2025/7/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MultimodalServiceImpl extends ServiceImpl<MultimodalMapper,Multimodal> implements MultimodalService {

    private final OllamaChatModel ollamaChatModel;
    private final MultimodalMapper multimodalMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String imageAnalyze(Long userId,String filename) {
        log.info("开始分析上传的图像文件: {}", filename);
        Multimodal multimodal = new Multimodal();
        try {
            // 校验用户id
            ValidationUtil.validateUserId(userId);
            // 校验文件名
            ValidationUtil.validateFilename(filename);
            // 构建文件路径
            Path filepath = Paths.get(IMAGE_UPLOAD_DIR, filename);
            if (!Files.exists(filepath)) {
                throw new CustomerException(ResultEnum.FILE_NOT_FOUND);
            }
            // 读取图像文件
            byte[] imageBytes = Files.readAllBytes(filepath);
            // 获取文件扩展名来确定MIME类型
            MimeType mimeType = getMimeTypeFromFilename(filename);

            multimodal.setUserId(userId);
            multimodal.setFilename(filename);
            multimodal.setFileType(1);
            multimodal.setFilePath(filepath.toString());
            multimodal.setStatus(1); //分析中
            multimodal.setCreateTime(LocalDateTime.now());
            multimodalMapper.insert(multimodal);

            //执行分析
            String result = analyzeImage(imageBytes, mimeType);
            log.info("图像文件分析成功: {}", filename);

            // 成功时更新结果和状态
            multimodal.setAnalysisResult(result);
            multimodal.setStatus(2); // 分析完成
            multimodalMapper.updateById(multimodal);

            return result;
            
        } catch (CustomerException e) {
            log.error("图像文件分析参数异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("图像文件分析系统异常，filename: {}", filename, e);

            // 仅当数据已插入数据库（multimodelId非null）时，才更新失败状态
            if (multimodal.getMultimodelId() != null) {
                Multimodal updateEntity = new Multimodal();
                updateEntity.setMultimodelId(multimodal.getMultimodelId());
                updateEntity.setStatus(3); // 失败状态
                multimodalMapper.updateById(updateEntity);
            }

            throw new CustomerException(ResultEnum.MULTIMODAL_ANALYSIS_FAILED);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String textAnalyze(Long userId,String filename) {
        log.info("开始分析上传的文本文件: {}", filename);
        Multimodal multimodal = new Multimodal();//1.作用在方法内（包括try和异常分支）  2.提前实例化，确保multimodal始终非null
        try {
            // 校验用户id
            ValidationUtil.validateUserId(userId);
            // 校验文件名
            ValidationUtil.validateFilename(filename);
            // 构建文件路径
            Path filepath = Paths.get(TEXT_UPLOAD_DIR, filename);
            if (!Files.exists(filepath)) {
                throw new CustomerException(ResultEnum.FILE_NOT_FOUND);
            }
            // 读取文本文件
            String textContent = Files.readString(filepath);
            if (!StringUtils.hasText(textContent)) {
                throw new CustomerException(ResultEnum.FILE_CONTENT_EMPTY);
            }


            multimodal.setUserId(userId);
            multimodal.setFilename(filename);
            multimodal.setFileType(2);
            multimodal.setFilePath(filepath.toString());
            multimodal.setContent(textContent);
            multimodal.setStatus(1); //分析中
            multimodal.setCreateTime(LocalDateTime.now());
            multimodalMapper.insert(multimodal);


            String result = analyzeText(textContent);
            log.info("文本文件分析成功: {}", filename);

            // 成功时更新分析结果和状态
            multimodal.setAnalysisResult(result);
            multimodal.setStatus(2); // 分析完成
            multimodalMapper.updateById(multimodal);


            return result;
        } catch (CustomerException e) {
            log.error("文本文件分析参数异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("文本文件分析系统异常，filename: {}", filename, e);
            // 仅当数据已插入数据库（multimodelId非null）时，才更新失败状态
            if (multimodal.getMultimodelId() != null) {
                Multimodal updateEntity = new Multimodal();
                updateEntity.setMultimodelId(multimodal.getMultimodelId());
                updateEntity.setStatus(3); // 失败状态
                multimodalMapper.updateById(updateEntity);
            }
                throw new CustomerException(ResultEnum.MULTIMODAL_ANALYSIS_FAILED);
            }

    }

    @Override
    public List<Multimodal> getMultimodelByUserId(Long userId) {
        log.info("获取用户多模态列表，userId: {}", userId);
        ValidationUtil.validateUserId(userId);
        //查询用户的多模态记录
        //按时间顺序排列
        List<Multimodal> multimodalList = this.lambdaQuery()
                .eq(Multimodal::getUserId, userId)
                .orderByAsc(Multimodal::getCreateTime)
                .list();
        //如果为空
        if(multimodalList==null || multimodalList.isEmpty()){
            log.info("用户 {} 的多模态列表为空", userId);
            return multimodalList;
        }

        log.info("获取用户多模态列表成功，userId: {}, 多模态数量: {}", userId, multimodalList.size());
        return multimodalList;
    }


    /**
     * 根据文件名获取MIME类型
     */
    private MimeType getMimeTypeFromFilename(String filename) {
        String ext = StringUtils.getFilenameExtension(filename);
        if (ext == null) {
            throw new CustomerException(ResultEnum.UNSUPPORTED_FILE_TYPE);
        }
        return switch (ext.toLowerCase()) {
            case "jpg", "jpeg" -> MimeTypeUtils.IMAGE_JPEG;
            case "png" -> MimeTypeUtils.IMAGE_PNG;
            case "gif" -> MimeTypeUtils.IMAGE_GIF;
            case "webp" -> MimeType.valueOf("image/webp");
            default -> throw new CustomerException(ResultEnum.UNSUPPORTED_FILE_TYPE);
        };
    }

    /**
     * 图像分析核心方法
     */
    private String analyzeImage(byte[] imageBytes, MimeType mimeType) {
        try {
            ByteArrayResource byteArrayResource = new ByteArrayResource(imageBytes);
            Media media = new Media(mimeType, byteArrayResource);
            OllamaOptions ollamaOptions = OllamaOptions.builder()
                    .model("gemma3:4b") //gemma3:4b llava:7b
                    .temperature(0.7)
                    .topP(0.7)
                    .build();

            Prompt prompt = new Prompt(
                    List.of(
                            SystemMessage.builder()
                                    .text(DEFAULT_SYSTEM_MESSAGE_PROMPT_MULTI_MODE_IMAGE_RECOGNITION)
                                    .build(),
                            UserMessage.builder()
                                    .media(media)
                                    .text("识别图片")
                                    .build()
                    ),
                    ollamaOptions
            );

            ChatResponse chatResponse = ollamaChatModel.call(prompt);
            return chatResponse.getResult().getOutput().getText();

        } catch (Exception e) {
            log.error("图像AI分析失败", e);
            throw new CustomerException(ResultEnum.MULTIMODAL_ANALYSIS_FAILED);
        }
    }

    /**
     * 文本分析核心方法
     */
    private String analyzeText(String textContent) {
        try {
            OllamaOptions ollamaOptions = OllamaOptions.builder()
                    .model("gemma3:4b") //gemma3:4b  llama3.2:3b
                    .temperature(0.7)
                    .topP(0.7)
                    .build();

            Prompt prompt = new Prompt(
                    List.of(
                            SystemMessage.builder()
                                    .text(DEFAULT_SYSTEM_MESSAGE_PROMPT_MULTI_MODE_TEXT_RECOGNITION)
                                    .build(),
                            UserMessage.builder()
                                    .text("请分析以下文本内容：\n" + textContent)
                                    .build()
                    ),
                    ollamaOptions
            );

            ChatResponse chatResponse = ollamaChatModel.call(prompt);
            return chatResponse.getResult().getOutput().getText();

        } catch (Exception e) {
            log.error("文本AI分析失败", e);
            throw new CustomerException(ResultEnum.MULTIMODAL_ANALYSIS_FAILED);
        }
    }

}
