package com.cjx.ollama.service.impl;

import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.HtmlBeautifyService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.atomic.AtomicInteger;

import static com.cjx.ollama.utils.constant.CSS.BEAUTIFUL_CSS_1;
import static com.cjx.ollama.utils.constant.Export.SESSION_HTML_LOCATION;

/**
 * HTML美化服务实现类
 * Author: cjx
 * Date: 2025/7/8
 */
@Slf4j
@Service
public class HtmlBeautifyServiceImpl implements HtmlBeautifyService {

    @Override
    public String beautifyHtml(Long userId) {
        ValidationUtil.validateUserId(userId);
        log.info("开始美化HTML文件");

        //processedCount            ：美化个数
        //alreadyBeautifiedCount    ：已美化个数
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger alreadyBeautifiedCount = new AtomicInteger(0);
        
        try {
            // 检查目录是否存在
            Path directory = Paths.get(SESSION_HTML_LOCATION);
            if (!Files.exists(directory)) {
                log.warn("目录不存在: {}", SESSION_HTML_LOCATION);
                throw new CustomerException(ResultEnum.HTML_FILE_NOT_FOUND);
            }
            
            try (var pathsStream = Files.walk(directory)) {
                var pathList = pathsStream.filter(path -> path.toString().endsWith(".html")).toList();
                
                if (pathList.isEmpty()) {
                    log.info("目录中没有找到HTML文件: {}", SESSION_HTML_LOCATION);
                    throw new CustomerException(ResultEnum.HTML_FILE_NOT_FOUND);
                }



                for (var path : pathList) {
                    //调用handleSingleHtmlFile处理单个HTML文件
                    //handleSingleHtmlFile内部调用processSingleHtmlFile判断文件是否处理
                    handleSingleHtmlFile(path, processedCount, alreadyBeautifiedCount);
                }

                if (processedCount.get() == 0) {
                    throw new CustomerException(ResultEnum.NO_HTML_NEED_BEAUTIFY);
                }


                String resultMessage = String.format("美化完成！总文件数：%d，新美化：%d，已美化：%d", 
                    pathList.size(), processedCount.get(), alreadyBeautifiedCount.get());
                
                log.info(resultMessage);
                return resultMessage;
            }
        } catch (CustomerException e) {
            log.error("HTML美化参数异常: {}", e.getMessage());
            throw e;
        } catch (IOException e) {
            log.error("美化HTML文件失败", e);
            throw new CustomerException(ResultEnum.HTML_BEAUTIFY_FAILED);
        } catch (Exception e) {
            log.error("美化HTML文件系统异常", e);
            throw new CustomerException(ResultEnum.HTML_BEAUTIFY_FAILED);
        }
    }

    /**
     * 处理单个HTML文件
     */
    private void handleSingleHtmlFile(Path path, AtomicInteger processedCount, AtomicInteger alreadyBeautifiedCount) {
        try {
            //调用processSingleHtmlFile判断文件是否处理
            boolean result = processSingleHtmlFile(path);
            if (result) {
                processedCount.incrementAndGet();
            } else {
                alreadyBeautifiedCount.incrementAndGet();
            }
        } catch (Exception e) {
            log.error("处理HTML文件失败: {}", path, e);
        }
    }

    /**
     * 处理单个 HTML 文件，返回是否处理成功
     * @param path 文件路径
     * @return 处理成功返回 true，失败返回 false
     */
    private boolean processSingleHtmlFile(Path path) {
        try {
            String content = Files.readString(path);
            
            // 判断是否已经美化过
            if (content.contains("<!-- beautified-by-ollama -->")) {
                log.debug("文件已经美化过，跳过: {}", path);
                return false;
            }
            
            String newContent;
            if (content.contains("<style>")) {
                newContent = content.replaceAll("(?s)<style>.*?</style>", BEAUTIFUL_CSS_1);
            } else {
                newContent = content.replace("<head>", "<head>" + BEAUTIFUL_CSS_1);
            }
            
            // 添加美化标记
            newContent = newContent.replace("</body>", "<!-- beautified-by-ollama -->\n</body>");
            
            Files.writeString(path, newContent);
            log.debug("HTML文件美化成功: {}", path);
            return true;
            
        } catch (IOException e) {
            log.error("处理HTML文件失败: {}", path, e);
            return false;
        }
    }
}
