package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.mapper.UserMapper;
import com.cjx.ollama.pojo.dto.UserDto;
import com.cjx.ollama.pojo.entity.User;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.UserService;
import com.cjx.ollama.utils.jwt.JwtUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 用户服务实现类
 * Author: cjx
 * Date: 2025/7/4
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public String register(UserDto userDto) {
        //DTO 只负责数据传递和校验
        String userName = userDto.getUserName();

        log.info("用户注册，userName: {}", userName);
        
        // 参数校验
        ValidationUtil.validateUsername(userName);
        ValidationUtil.validatePassword(userDto.getPassWord());
        
        // 检查用户名是否已存在
        if (this.lambdaQuery().eq(User::getUserName, userName).count() > 0) {
            throw new CustomerException(ResultEnum.USERNAME_EXIT);
        }

        // 创建用户
        User user = new User();
        user.setUserName(userName);
        user.setPassWord(userDto.getPassWord());
        
        boolean saved = this.save(user);
        if (!saved) {
            throw new CustomerException(ResultEnum.USER_REGISTER_FAILED);
        }
        
        log.info("用户 [{}] 注册成功", userName);
        return "注册成功";
    }

    @Override
    public String login(UserDto userDto) {
        String userName = userDto.getUserName();

        log.info("用户登录，userName: {}", userName);
        
        // 参数校验
        ValidationUtil.validateUsername(userName);
        ValidationUtil.validatePassword(userDto.getPassWord());
        
        // 查询用户
        User user = this.lambdaQuery()
                .eq(User::getUserName, userName)
                .one();

        if (user == null) {
            throw new CustomerException(ResultEnum.USERNAME_NOT_EXIST);
        }

        // 用户存在，再校验密码
        if (!userDto.getPassWord().equals(user.getPassWord())) {
            // 密码错误，抛出密码错误对应的异常
            throw new CustomerException(ResultEnum.PASSWORD_ERROR);
        }

        // 生成JWT token
        String token = JwtUtil.tokenGenerate(user.getUserId().toString());
        log.info("用户 [{}] 登录成功\n  token: {}", userName, token);

        return token;
    }

    @Override
    //使当前 JWT token 失效，用户需要重新登录
    //    将当前 token 加入黑名单
    //    用户数据保留
    //    下次请求时拦截器会拒绝该 token
    public String quit(Long userId) {
        log.info("用户退出，userId: {}", userId);

        // 参数校验
        ValidationUtil.validateUserId(userId);

        // 获取当前请求的token
        String token = getCurrentToken();
        if (token != null) {
            // 将token加入黑名单
            JwtUtil.addToBlacklist(token);
            log.info("用户 [{}] 退出成功，token已加入黑名单", userId);
        } else {
            log.warn("用户 [{}] 退出时未找到token", userId);
        }

        return "退出成功";
    }

    @Override
//    功能：使当前 JWT token 失效
//    实现：
//    将当前 token 加入黑名单
//    目前保留用户数据（注释了删除逻辑）
//    如需真正删除用户数据，可取消注释
    public String logout(Long userId) {
        log.info("用户注销，userId: {}", userId);
        // 参数校验
        ValidationUtil.validateUserId(userId);
        // 获取当前请求的token
        String token = getCurrentToken();
        if (token != null) {
            // 将token加入黑名单
            JwtUtil.addToBlacklist(token);
            log.info("用户 [{}] 注销成功，token已加入黑名单", userId);
        } else {
            log.warn("用户 [{}] 注销时未找到token", userId);
        }

        // 删除用户相关的所有数据
        // 1. 删除用户的会话
        // 2. 删除用户的消息
        // 3. 删除用户上传的文件
        // 4. 删除用户账号
        try {
            // 在删除用户数据前，先获取用户信息以删除avatar文件
            User user = this.getById(userId);
            // 删除用户avatar文件
            if (user != null && user.getAvatar() != null && !user.getAvatar().isEmpty()) {
                deleteAvatarFile(user.getAvatar());
            }
            boolean deleted = this.removeById(userId);
            if (deleted) {
                log.info("用户 [{}] 数据已完全删除", userId);
                return "注销成功，用户数据已删除";
            } else {
                log.warn("用户 [{}] 数据删除失败", userId);
                return "注销成功，但用户数据删除失败";
            }
        } catch (Exception e) {
            log.error("删除用户数据时发生异常，userId: {}", userId, e);
            return "注销成功，但用户数据删除时发生异常";
        }
    }

    @Override
    public User getUserInfo(Long userId) {
        log.info("获取用户信息，userId: {}", userId);
        ValidationUtil.validateUserId(userId);
        User user = this.getById(userId);
        if (user != null) {
            return user;
        }else{
            log.warn("用户信息未找到，userId: {}", userId);
            throw new CustomerException(ResultEnum.USER_NOT_FOUND);
        }
    }

    @Override
    public List<User> getUserList() {
        return this.list();

    }

    @Override
    public void updateUser(User user) {
        this.updateById(user);
        log.info("用户信息更新成功，userId: {}", user.getUserId());
    }

    @Override
    public void deleteUser(Long userId) {
        ValidationUtil.validateUserId(userId);
        this.removeById(userId);
        log.info("用户删除成功，userId: {}", userId);
    }

    /**
     * 获取当前请求的token
     * @return token字符串，如果未找到则返回null
     * 支持Authorization 头 和 自定义 token 头
     */
    private String getCurrentToken() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.debug("当前请求上下文为空，无法获取token");
                return null;
            }

            HttpServletRequest request = attributes.getRequest();
            String token ;

            // 1. 优先从Authorization头（Bearer格式）获取
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7).trim();
                log.debug("从Authorization头获取到token（Bearer格式）");
                return token;
            }

            // 2. 如果Authorization头无效，尝试从token头获取
            token = request.getHeader("token");
            if (token != null && !token.isEmpty()) {
                log.debug("从token头获取到token");
                return token.trim();
            }

            // 3. 未找到token
            log.debug("未从Authorization或token头中找到有效token");
            return null;

        } catch (Exception e) {
            log.error("获取当前请求token失败", e);
            return null;
        }
    }

    /**
     * 删除用户avatar文件
     * @param avatarUrl
     * avatar文件URL：/upload/image/filename
     * 处理文件可能已被手动删除的情况，确保删除操作的健壮性
     */
    private void deleteAvatarFile(String avatarUrl) {
        try {
            if (avatarUrl.startsWith("/")) {
                avatarUrl = avatarUrl.substring(1); // 移除开头的斜杠
            }

            Path filePath = Paths.get(avatarUrl);
            // 记录详细信息，便于调试
            log.debug("尝试删除用户avatar文件: URL={}, 相对路径={}", avatarUrl, filePath);

            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("用户avatar文件删除成功: {}", filePath);
            } else {
                // 文件不存在的情况，记录警告但不影响注销流程
                log.warn("用户avatar文件不存在，可能已被手动删除: {}", filePath);
            }
        } catch (Exception e) {
            // 文件删除失败不应该影响用户注销流程，记录错误但继续执行
            log.error("删除用户avatar文件失败: {}, 错误: {}", avatarUrl, e.getMessage());
            // 注意：这里不抛出异常，避免因文件删除失败影响用户注销
        }
    }
}
