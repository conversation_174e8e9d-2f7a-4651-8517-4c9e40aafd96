package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.Result;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.pojo.info.StreamInfo;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.service.ChatSessionService;
import com.cjx.ollama.service.ChatService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.cjx.ollama.utils.constant.Prompt.DEFAULT_SYSTEM_MESSAGE_PROMPT;

/**
 * 聊天服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements ChatService {

    private final OllamaChatModel ollamaChatModel;
    private final ChatMessageService chatMessageService;
    private final ChatSessionService chatSessionService;

    @Value("${spring.ai.ollama.chat.model}")
    private String modelName;
    @Override
    public Mono<Result<String>> getModel() {
        log.info("当前对话模型: {}", modelName);
        return Mono.just(Result.success(modelName));
    }

    @Override
    public String chatCall(
            Long userId,
            String message,
            String sessionId
    ) {
        log.info("开始处理普通对话，userId: {}, sessionId: {}", userId, sessionId);

        // 校验参数并处理会话ID
        String activeSessionId = validateAndHandleSessionId(userId, message, sessionId);
        // 自动更新会话标题
        updateSessionName(activeSessionId, message);
        // 保存用户消息
        saveUserMessage(activeSessionId, message, "user_call");
        // AI回复
        String chatResponse = ollamaChatModel.call(
                new SystemMessage(DEFAULT_SYSTEM_MESSAGE_PROMPT),
                new UserMessage(message)
        );
        // 保存AI消息
        saveAssistantMessage(activeSessionId, chatResponse, "AI_CALL");
        // 更新会话最后消息
        updateLastMessage(activeSessionId, chatResponse);
        log.info("普通对话完成，sessionId: {}", activeSessionId);
        return chatResponse;
    }

    @Override
    public Flux<ServerSentEvent<String>> chatStream(
            Long userId,
            String message,
            String sessionId,
            String streamId
    ) {
        log.info("开始处理流式对话，userId: {}, sessionId: {}, streamId: {}", userId, sessionId, streamId);
        // 校验streamId
        ValidationUtil.validateStreamId(streamId);
        // 使用封装方法而不是直接操作 map
        StreamInfo.putStreamInfo(streamId, new StreamInfo());
        // 校验参数并处理会话ID
        String activeSessionId = validateAndHandleSessionId(userId, message, sessionId);
        // 自动更新会话标题
        updateSessionName(activeSessionId, message);
        // 保存用户消息
        saveUserMessage(activeSessionId, message, "user_stream");
        StringBuilder chatResponse = new StringBuilder();
        return ollamaChatModel.stream(
                        new SystemMessage(DEFAULT_SYSTEM_MESSAGE_PROMPT),
                        new UserMessage(message)
                )
                .takeUntil(tokenElement -> {
                    StreamInfo streamInfo = StreamInfo.getStreamInfo(streamId);
                    return streamInfo != null && streamInfo.getStopFlag().get();
                })
                .map(tokenElement -> {
                    chatResponse.append(tokenElement);
                    return ServerSentEvent
                            .builder(tokenElement)
                            .event("message")
                            .build();
                })
                .doOnComplete(() -> {
                    try {
                        saveAssistantMessage
                                (
                                        activeSessionId,
                                        chatResponse.toString(),
                                        "AI_STREAM"
                                );
                        updateLastMessage
                                (
                                        activeSessionId,
                                        chatResponse.toString()
                                );
                        log.info("流式对话完成，streamId: {}", streamId);
                    } catch (Exception e) {
                        log.error("保存流式对话消息失败，streamId: {}", streamId, e);
                    } finally {
                        // 流结束时清理资源
                        StreamInfo.removeStreamInfo(streamId);
                    }
                })
                .doOnError(error -> {
                    log.error("流式对话异常，streamId: {}", streamId, error);
                    StreamInfo.removeStreamInfo(streamId);
                });
    }

    @Override
    public boolean stopStream(String streamId) {
        log.info("收到终止流请求，streamId: {}", streamId);

        ValidationUtil.validateStreamId(streamId);

        StreamInfo streamInfo = StreamInfo.getStreamInfo(streamId);
        if (streamInfo != null) {
            if (streamInfo.getStopFlag().get()) {
                log.info("流已提前终止，无需重复终止，streamId: {}", streamId);
                return false;
            }

            streamInfo.getStopFlag().set(true);
            log.info("流已终止，streamId: {}", streamId);
            return true;
        }

        log.warn("未找到流，streamId: {}", streamId);
        return false;
    }



    /**
     * 校验参数并处理会话ID
     */
    private String validateAndHandleSessionId(Long userId, String message, String sessionId) {
        // 处理会话ID
        if (StringUtils.isBlank(sessionId)) {
            sessionId = UUID.randomUUID().toString();
            ChatSession chatSession = new ChatSession()
                    .setUserId(userId)
                    .setSessionId(sessionId)
                    .setSessionName(message.length() >= 15 ? message.substring(0, 15) : message)
                    .setLastMessage("新会话已创建")
                    .setCreateTime(LocalDateTime.now());

            try {
                chatSessionService.save(chatSession);
                log.info("新会话创建成功: userId={}, sessionId={}, ",userId, sessionId);
            } catch (Exception e) {
                log.error("创建会话失败: userId={}", userId, e);
                throw new CustomerException(ResultEnum.REQUEST_SUCCESS);
            }
        }
        return sessionId;
    }

    /**
     * 保存用户消息
     */
    private void saveUserMessage(String sessionId, String message, String sender) {
        ChatMessage userMsg = new ChatMessage();
        userMsg.setSessionId(sessionId);
        userMsg.setSender(sender);
        userMsg.setContent(message);
        userMsg.setCreateTime(LocalDateTime.now());
        chatMessageService.save(userMsg);
    }

    /**
     * 保存AI助手消息
     */
    private void saveAssistantMessage(String sessionId, String message, String sender) {
        //message=<think>...<think>+正文
        //提取分离
        String think = "";
        String content = message;
        // 提取 <think>...</think> 中的内容
        Pattern pattern = Pattern.compile("<think>([\\s\\S]*?)</think>");
        Matcher matcher = pattern.matcher(message);
        if (matcher.find()) {
            think = matcher.group(1).trim();
            // 去除 think 标签部分，得到正文
            content = message.replace(matcher.group(0), "").trim();
        }

        ChatMessage assistant = new ChatMessage();
        assistant.setSessionId(sessionId);
        assistant.setSender(sender);
        assistant.setThink(think);
        assistant.setContent(content);
        assistant.setCreateTime(LocalDateTime.now());
        chatMessageService.save(assistant);
    }

    /**
     * 自动更新会话标题
     */
    private void updateSessionName(String sessionId, String message) {
        try {
            ChatSession session = chatSessionService.getById(sessionId);
            if (session != null && "未命名会话".equals(session.getSessionName())) {
                String newSessionName = message.length() > 15 ? message.substring(0, 15) + "..." : message;
                session.setSessionName(newSessionName);
                chatSessionService.updateById(session);
                log.info("会话标题已更新: sessionId={}, 新标题={}", sessionId, newSessionName);
            }
        } catch (Exception e) {
            log.error("更新会话标题失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 更新会话的最后一条消息
     */
    private void updateLastMessage(String sessionId, String message) {
        try {
            ChatSession session = chatSessionService.getById(sessionId);
            if (session != null) {
                String truncatedMsg = message;
                if (message != null && message.length() > 100) {
                    truncatedMsg = message.substring(0, 100) + "...";
                }

                session.setLastMessage(truncatedMsg);
                chatSessionService.updateById(session);

                if (truncatedMsg != null) {
                    log.info("更新会话最后消息成功: sessionId={}, 消息长度={}", sessionId, truncatedMsg.length());
                } else {
                    log.warn("截断消息为NULL: sessionId={}", sessionId);
                }
            } else {
                log.warn("未找到会话: sessionId={}", sessionId);
            }
        } catch (Exception e) {
            log.error("更新会话最后消息失败: sessionId={}", sessionId, e);
        }
    }
} 