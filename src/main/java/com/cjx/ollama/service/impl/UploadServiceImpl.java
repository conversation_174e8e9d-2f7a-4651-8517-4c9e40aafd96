package com.cjx.ollama.service.impl;

import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.UploadService;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static com.cjx.ollama.utils.constant.Upload.*;

/**
 * 文件上传服务实现类
 * Author: cjx
 * Date: 2025/7/8
 */
@Slf4j
@Service
public class UploadServiceImpl implements UploadService {

    @Override
    public String uploadImage(
            Long userId,
            MultipartFile file
    ) {
        log.info("开始上传图片文件");
        try {
            // 校验用户id
            ValidationUtil.validateUserId(userId);
            // 校验文件是否为空
            ValidationUtil.validateFileNotEmpty(file);
            // 验证文件类型
            ValidationUtil.validateImageFile(file);

            // 保证目录存在
            ensureDirectoryExist(IMAGE_UPLOAD_DIR);

            // 生成唯一文件名
            String filename = generateUniqueFilename(file.getOriginalFilename());

            Path filepath = Paths.get(IMAGE_UPLOAD_DIR, filename);
            Files.write(filepath, file.getBytes());

            // 返回图片访问路径（添加前缀斜杠，用于Web访问）
            String fileUrl = "/" + IMAGE_UPLOAD_DIR + filename;
            log.info("图片上传成功: {}", fileUrl);
            return fileUrl;
            
        } catch (CustomerException e) {
            log.error("图片上传参数异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("图片上传系统异常", e);
            throw new CustomerException(ResultEnum.FILE_UPLOAD_FAILED);
        }
    }

    @Override
    public String uploadText(
            Long userId,
            MultipartFile file
    ) {
        log.info("开始上传文本文件");
        try {
            // 校验用户id
            ValidationUtil.validateUserId(userId);
            // 校验文件是否为空
            ValidationUtil.validateFileNotEmpty(file);
            // 验证文件类型
            ValidationUtil.validateTextFile(file);

            // 保证目录存在
            ensureDirectoryExist(TEXT_UPLOAD_DIR);

            // 生成唯一文件名
            String filename = generateUniqueFilename(file.getOriginalFilename());

            Path filepath = Paths.get(TEXT_UPLOAD_DIR, filename);
            Files.write(filepath, file.getBytes());

            // 返回文件访问路径（添加前缀斜杠，用于Web访问）
            String fileUrl = "/" + TEXT_UPLOAD_DIR + filename;
            log.info("文本文件上传成功: {}", fileUrl);
            return fileUrl;
            
        } catch (CustomerException e) {
            log.error("文本文件上传参数异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("文本文件上传系统异常", e);
            throw new CustomerException(ResultEnum.FILE_UPLOAD_FAILED);
        }
    }

    /**
     * 确保目录存在
     */
    private void ensureDirectoryExist(
            String directoryPath
    ) {
        File dir = new File(directoryPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                log.error("创建目录失败: {}", directoryPath);
                throw new CustomerException(ResultEnum.DIRECTORY_CREATE_FAILED);
            }
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFilename(
            String originalFilename
    ) {
        //获取文件名称的扩展名
        String ext = StringUtils.getFilenameExtension(originalFilename);
        //生成一个包含当前时间戳和文件扩展名（可选）的字符串
        return System.currentTimeMillis() + (ext != null ? ("." + ext) : "");
    }
}
