package com.cjx.ollama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjx.ollama.pojo.entity.ChatSession;

/**
 * Author: cjx
 * Date: 2025/7/18
 */
public interface ExportService extends IService<ChatSession> {
    /**
     * 导出会话标题列表
     * @return 会话标题字符串（换行分隔）
     */
    String exportSessionNameTxt(Long userId);

    /**
     * 导出会话为HTML
     * @param sessionId 会话ID
     * @return HTML内容
     */
    String exportSessionHtml(Long userId, String sessionId, String imageCategory );

    /**
     * 导出会话为XML
     * @param sessionId 会话ID
     * @return XML内容
     */
    String exportSessionXml(Long userId, String sessionId, String postCategory, String imageCategory );

    /**
     * 导出会话为EXCEL
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param postCategory 文章分类
     * @param imageCategory 图片分类
     * @return EXCEL内容（Base64编码）
     */
    String exportSessionExcel(Long userId, String sessionId, String postCategory, String imageCategory);

    /**
     * 导出会话为CSV
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param postCategory 文章分类
     * @param imageCategory 图片分类
     * @return CSV内容字符串
     */
    String exportSessionCsv(Long userId, String sessionId, String postCategory, String imageCategory);
}
