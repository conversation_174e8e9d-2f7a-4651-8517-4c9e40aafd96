package com.cjx.ollama.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传服务接口
 * Author: cjx
 * Date: 2025/7/8
 */
public interface UploadService {
    
    /**
     * 图片上传
     * @param file 图片文件
     * @return 图片访问路径
     */
    String uploadImage(Long userId, MultipartFile file);
    
    /**
     * 文本文件上传
     * @param file 文本文件
     * @return 文件访问路径
     */
    String uploadText(Long userId, MultipartFile file);


}
