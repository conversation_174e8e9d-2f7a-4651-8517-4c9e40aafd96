package com.cjx.ollama.scheduler.link;

import com.cjx.ollama.component.LinkLoader;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Author: cjx
 * Date: 2025/7/28
 * 定时任务
 */
@Component
@RequiredArgsConstructor
public class LinkCacheScheduler {
    private final LinkLoader linkLoader;
    @Scheduled(fixedRate = 10 * 60 * 1000) // 每 10 分钟执行一次
    public void autoRefreshCache() {
        linkLoader.refreshCache();
    }
}
// 每天凌晨 2 点执行一次刷新缓存
//@Scheduled(cron = "0 0 2 * * ?")