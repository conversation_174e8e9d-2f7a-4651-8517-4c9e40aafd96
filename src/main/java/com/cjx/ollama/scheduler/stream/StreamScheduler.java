package com.cjx.ollama.scheduler.stream;

import com.cjx.ollama.pojo.info.StreamInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

import static com.cjx.ollama.utils.constant.Stream.STREAM_EXPIRE_TIME;

/**
 * 定期清理过期的流状态（超过5分钟）
 */
@Slf4j
@Component
public class StreamScheduler {



    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanupExpiredStreams() {
        try {
            long currentTime = System.currentTimeMillis();

            ConcurrentMap<String, StreamInfo> map = StreamInfo.getAll();

            int removedCount = 0;

            Iterator<Map.Entry<String, StreamInfo>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, StreamInfo> entry = iterator.next();
                if (currentTime - entry.getValue().getCreateTime() > STREAM_EXPIRE_TIME) {
                    iterator.remove(); // 安全删除
                    removedCount++;
                }
            }

            log.debug("清理过期流状态，共清除 {} 条，当前活跃流数量: {}", removedCount, map.size());

        } catch (Exception e) {
            log.error("清理过期流状态失败", e);
        }
    }

}
