package com.cjx.ollama;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

@Slf4j
//@EnableScheduling //暂不需要定时刷新，移除相关定时任务
@SpringBootApplication
public class OllamaApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(OllamaApplication.class, args);
        Environment env = context.getEnvironment();
        String port = env.getProperty("server.port");
        log.info("OllamaApplication服务启动成功 " + " 端口:{}" , port);

    }

}
