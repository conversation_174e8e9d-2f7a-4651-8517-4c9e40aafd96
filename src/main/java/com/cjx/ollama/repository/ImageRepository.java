package com.cjx.ollama.repository;

import com.cjx.ollama.mapper.ImageMapper;
import com.cjx.ollama.pojo.entity.Image;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: cjx
 * Date: 2025/7/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageRepository {
    private final ImageMapper imageMapper;
    /**
     * 获取所有图片链接
     */
    public List<Image> getImageList(){
        log.info("ImageRepository初始化图片链接成功");
        return imageMapper.selectList(null);
    }
}
