package com.cjx.ollama.repository;

import com.cjx.ollama.mapper.LinkMapper;
import com.cjx.ollama.pojo.entity.Link;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 链接数据访问服务
 * 专门负责数据库操作，不依赖业务逻辑层
 * “Repository” 是领域驱动设计（DDD）中的标准术语，专门指代 “数据仓库”，负责数据的查询和存储，不包含业务逻辑，与你 “解耦数据库操作、独立数据访问” 的定位完全匹配。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LinkRepository {

    private final LinkMapper linkMapper;

    /**
     * 获取所有链接数据
     */
    public List<Link> getLinkList() {
        log.info("LinkRepository初始化链接成功");
        return linkMapper.selectList(null);
    }
}