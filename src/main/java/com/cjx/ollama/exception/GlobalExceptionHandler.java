package com.cjx.ollama.exception;

import com.cjx.ollama.result.Result;
import com.cjx.ollama.result.ResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Author: cjx
 * Date: 2025/7/8
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(CustomerException.class)
    public Result<String> handleCustomerException(CustomerException e) {
        log.error("捕获到自定义异常，code: {}, message: {}",
                e.getResultEnum().getCode(),
                e.getResultEnum().getMessage());
        return Result.error(e.getResultEnum());
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public Result<String> handleException(Exception e) {
        log.error("系统异常:  ", e);
        return Result.error(ResultEnum.REQUEST_SUCCESS, "系统内部错误");
    }
}
