package com.cjx.ollama.exception;

import com.cjx.ollama.result.ResultEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author: cjx
 * Date: 2025/7/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerException extends RuntimeException{  //运行时异常 无需在方法上声明 全局异常处理器即可捕获

    private final ResultEnum resultEnum;

    public CustomerException(
            ResultEnum resultEnum
    ){
        super(resultEnum.getMessage());
        this.resultEnum=resultEnum;
    }

}
