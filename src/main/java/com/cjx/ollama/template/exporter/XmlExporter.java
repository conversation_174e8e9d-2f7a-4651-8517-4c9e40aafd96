package com.cjx.ollama.template.exporter;

import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.template.AbstractExportService;
import com.cjx.ollama.utils.session.export.FileWriteUtil;
import com.cjx.ollama.utils.session.export.MarkdownUtils;
import com.cjx.ollama.utils.session.export.WordPressXmlUtil;
import com.cjx.ollama.utils.session.export.image.ImageUtil;
import com.cjx.ollama.utils.session.export.link.LinkUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * XML导出器
 */
@Slf4j
public class XmlExporter extends AbstractExportService {
    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final String postCategory;
    private final String imageCategory;

    public XmlExporter(LinkUtil linkUtil, ImageUtil imageUtil, ImageLoader imageLoader,
                       String postCategory, String imageCategory, Long userId, String sessionId) {
        super(userId, sessionId);
        this.linkUtil = linkUtil;
        this.imageUtil = imageUtil;
        this.imageLoader = imageLoader;
        this.postCategory = postCategory;
        this.imageCategory = imageCategory;
    }

    @Override
    protected String doExport(ChatSession session, List<ChatMessage> messages) {
        StringBuilder contentBuilder = new StringBuilder();
        for (ChatMessage msg : messages) {
            String htmlContent = MarkdownUtils.processMessageContent(msg.getContent());
            String htmlWithLink = linkUtil.insertLinks(htmlContent);
            ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                    htmlWithLink, imageLoader.getImagesByCategory(imageCategory));
            contentBuilder.append(imageResult.htmlContent()).append("\n");
        }

        String xml = WordPressXmlUtil.generateWordPressXml(
                session, session.getSessionName(), contentBuilder.toString(), postCategory);
        FileWriteUtil.writeSessionXml(session, xml);

        log.info("XML 导出成功，userId: {}, sessionId: {}, 消息数量: {}",
                getUserId(), getSessionId(), messages.size());
        return xml;
    }
}