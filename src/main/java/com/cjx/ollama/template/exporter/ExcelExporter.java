package com.cjx.ollama.template.exporter;

import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.template.AbstractExportService;
import com.cjx.ollama.utils.session.export.EscapeUtil;
import com.cjx.ollama.utils.session.export.FileWriteUtil;
import com.cjx.ollama.utils.session.export.MarkdownUtils;
import com.cjx.ollama.utils.session.export.image.ImageUtil;
import com.cjx.ollama.utils.session.export.link.LinkUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.StringWriter;
import java.util.List;

/**
 * Excel导出器
 */
@Slf4j
public class ExcelExporter extends AbstractExportService {
    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final String postCategory;
    private final String imageCategory;

    public ExcelExporter(LinkUtil linkUtil, ImageUtil imageUtil, ImageLoader imageLoader,
                         String postCategory, String imageCategory, Long userId, String sessionId) {
        super(userId, sessionId);
        this.linkUtil = linkUtil;
        this.imageUtil = imageUtil;
        this.imageLoader = imageLoader;
        this.postCategory = postCategory;
        this.imageCategory = imageCategory;
    }

    @Override
    protected String doExport(ChatSession session, List<ChatMessage> messages) {
        try (StringWriter stringWriter = new StringWriter()) {
            stringWriter.write('\uFEFF');
            stringWriter.write("序号\t会话标题\t消息内容（HTML）\t文章分类\t图片链接\t关联链接\n");

            for (int i = 0; i < messages.size(); i++) {
                ChatMessage msg = messages.get(i);
                String htmlContent = MarkdownUtils.toHtml(msg.getContent());
                String htmlWithLink = linkUtil.insertLinks(htmlContent);
                ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                        htmlWithLink, imageLoader.getImagesByCategory(imageCategory));

                String line = String.format("%d\t%s\t%s\t%s\t%s\t%s%n",
                        i + 1,
                        EscapeUtil.escapeTabSeparated(session.getSessionName()),
                        EscapeUtil.escapeTabSeparated(imageResult.htmlContent()),
                        EscapeUtil.escapeTabSeparated(postCategory),
                        EscapeUtil.escapeTabSeparated(imageResult.imageUrl()),
                        "");
                stringWriter.write(line);
            }

            String excelTextContent = stringWriter.toString();
            FileWriteUtil.writeSessionExcel(session, excelTextContent);

            log.info("Excel 导出成功，userId: {}, sessionId: {}, 消息数量: {}",
                    getUserId(), getSessionId(), messages.size());
            return excelTextContent;

        } catch (Exception e) {
            log.error("Excel生成失败，sessionId: {}", getSessionId(), e);
            throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
        }
    }
}