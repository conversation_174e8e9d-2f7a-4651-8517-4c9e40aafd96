package com.cjx.ollama.template.exporter;

import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.template.AbstractExportService;
import com.cjx.ollama.utils.session.export.FileWriteUtil;
import com.cjx.ollama.utils.session.export.MarkdownUtils;
import com.cjx.ollama.utils.session.export.image.ImageUtil;
import com.cjx.ollama.utils.session.export.link.LinkUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * HTML导出器
 */
@Slf4j
public class HtmlExporter extends AbstractExportService {
    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final String imageCategory;

    public HtmlExporter(LinkUtil linkUtil, ImageUtil imageUtil, ImageLoader imageLoader,
                        String imageCategory, Long userId, String sessionId) {
        super(userId, sessionId);
        this.linkUtil = linkUtil;
        this.imageUtil = imageUtil;
        this.imageLoader = imageLoader;
        this.imageCategory = imageCategory;
    }

    @Override
    protected String doExport(ChatSession session, List<ChatMessage> messages) {
        String html = MarkdownUtils.generateSessionHtml(session, messages);
        String htmlWithLink = linkUtil.insertLinks(html);
        ImageUtil.ImageInjectionResult result = imageUtil.insertImageLink(
                htmlWithLink, imageLoader.getImagesByCategory(imageCategory));
        FileWriteUtil.writeSessionHtml(session, result.htmlContent());

        log.info("HTML 导出成功，userId: {}, sessionId: {}, 消息数量: {}",
                getUserId(), getSessionId(), messages.size());
        return result.htmlContent();
    }
}